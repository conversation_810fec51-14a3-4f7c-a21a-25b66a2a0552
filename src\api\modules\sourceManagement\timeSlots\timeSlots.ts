/**
 * @description 时段管理接口
 */

import { moduleRequest } from "@/api/request";
import { ReqId, TimeSlots } from "@/api/interface";
const http = moduleRequest("/admin/numbersource/");

const timeSlotsApi = {
  /** 获取时段列表 */
  page() {
    return http.post<TimeSlots.TimeSlotsInfo[]>("gettimeslot", {});
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "edittimeslot" : "addtimeslot", params);
  },
  /** 删除时段 */
  delete(params: string[]) {
    return http.post("deletetimeslot", params);
  },
  /**获取指定类型的时段*/
  getTimeSlotByType(params) {
    return http.post<TimeSlots.SpecifiedTimeSlotInfo[]>("gettimeslotbytype", params);
  }
};

export { timeSlotsApi };
