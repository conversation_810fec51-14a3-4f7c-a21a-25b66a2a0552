<template>
  <div class="unit-list-container">
    <el-row :gutter="20">
      <!-- 左侧：列表 -->
      <el-col :span="8">
        <el-card class="unit-card">
          <div id="unit-card-header" class="stickyTop">
            <div>
              <!-- <el-button type="danger" round>新增</el-button>
              <el-button type="primary" round>删除</el-button> -->
            </div>

            <!-- 搜索框 -->
            <div class="search-box">
              <el-input v-model="searchUnitKeyword" placeholder="搜索类型名称" clearable @input="filterUnits" prefix-icon="el-icon-search" />
            </div>
          </div>

          <!-- 列表 -->
          <div class="unit-list">
            <el-table
              :data="filteredUnits"
              style="width: 100%"
              border
              stripe
              :header-cell-style="{
                background: '#f5f7fa',
                color: '#303133',
                fontWeight: 'bold'
              }"
              height="calc(100vh - 150px)"
            >
              <el-table-column prop="label" label="类型" min-width="100">
                <template #default="scope">
                  <div @click="handleUnitSelect(scope.row)">
                    <span style="font-weight: bold; color: red" v-if="scope.row.typeCode === notice.typeCode">*</span>
                    {{ scope.row.label }}
                    <span style="font-weight: bold; color: red" v-if="scope.row.typeCode === notice.typeCode">*</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="状态" min-width="80">
                <template #default="scope">
                  <el-switch
                    v-auth="userButtonCode.操作用户"
                    :model-value="scope.row.status === CommonStatusEnum.ENABLE"
                    :loading="switchLoading"
                    @change="editStatus(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination-units stickyFooter">
            <el-pagination
              v-model:current-page="unitCurrentPage"
              v-model:page-size="unitPageSize"
              :pager-count="4"
              :page-sizes="[10, 50, 100]"
              layout="total, pager, next,sizes"
              :total="totalUnits"
              background
              @size-change="handleUnitSizeChange"
              @current-change="handleUnitCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="2"></el-col>
      <!-- 右侧： -->
      <el-col :span="13">
        <div v-if="notice.typeCode">
          <WangEditor v-model:value="content" height="400px" :key="editorKey" ref="wangEditor" :disabled="isDisabled" />
          <div style="display: flex; justify-content: center; margin: 5px 0">
            <el-button type="primary" round @click="EditNotices()" style="width: 10rem">保存</el-button>
          </div>
        </div>
        <el-empty v-else :description="notice.label" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { sysNotice, noticeApi, userButtonCode } from "@/api";
import WangEditor from "@/components/WangEditor/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useDictStore } from "@/stores/modules";
import { SysDictEnum, FormOptEnum, CommonStatusEnum, DictCategoryEnum } from "@/enums";
import { log } from "@antv/g2plot/lib/utils";

//选中的内容
const notice = ref<sysNotice.noticeTable>({
  id: "",
  typeCode: "",
  content: "",
  status: "",
  label: "",
  value: "",
  styleColor: false
});

//列表搜索
const searchUnitKeyword = ref("");
//列表分页
/***一页多少条 */
const unitPageSize = ref(10);
/***第几页 */
const unitCurrentPage = ref(1);
const totalUnits = ref(0);
//列表数据
const filteredUnits = ref<sysNotice.noticeTable[]>([]); //筛选后的列表数据
const unitList = ref<sysNotice.noticeTable[]>([]); //列表原始数据
const unitListB = ref<sysNotice.noticeTable[]>([]); //列表原始数据备份，用于筛选后恢复原数据

//状态开关loading
const switchLoading = ref(false);
const switchBut = ref(false);

//获取字段
// const dictStore = useDictStore(); //字典仓库
// const dctTypeOptions = dictStore.getDictList(SysDictEnum.PE_CLS);

//文本内容
const content = ref("");
/**禁用富文本 */
const isDisabled = ref(true);
/**触发编辑器重载 */
const editorKey = ref(0);
const wangEditor = ref();
/**监视富文本，当文本状态变化时，编辑器重载 */
watch(
  () => isDisabled.value,
  newValue => {
    if (newValue !== undefined) {
      editorKey.value++; // 触发编辑器重建
    }
  }
);
/***初始值 */
async function InitialValue() {
  notice.value.content = "";
  notice.value.label = "请点击左边类型编制内容";
  notice.value.id = "";
  notice.value.typeCode = "";
  notice.value.status = "";
  content.value = "";
  filteredUnits.value.length = 0;
  unitList.value.length = 0;
  unitListB.value.length = 0;
}
// 初始化
onMounted(() => {
  fetchUnitList();
});
/***获取列表数据Api */
const fetchUnitList = async () => {
  //初始化文本
  await InitialValue();
  await noticeApi
    .page({
      pageNum: unitCurrentPage.value,
      pageSize: unitPageSize.value,
      searchKey: "",
      id: "",
      typeCode: "",
      status: ""
    })
    .then(res => {
      if (res.code == "200") {
        // let dic_type = JSON.parse(JSON.stringify(dctTypeOptions));
        //获取体检类型
        let dic_type = res.data["peCls"].map(item => {
          return { value: item.clsCode, label: item.clsName };
        });
        dic_type.unshift({ value: "index", label: "首页" });
        //
        unitList.value = dic_type.map(x => {
          if (res.data["notices"].length == 0) {
            return {
              id: "",
              typeCode: x.value,
              status: "",
              content: "",
              label: x.label
            };
          } else {
            let y = res.data["notices"].find(z => z.typeCode == x.value);
            return y
              ? {
                  id: y.id,
                  typeCode: y.typeCode,
                  status: y.status,
                  content: y.content,
                  label: x.label
                }
              : {
                  id: "",
                  typeCode: x.value,
                  status: "",
                  content: "",
                  label: x.label
                };
          }
        });
        totalUnits.value = unitList.value.length;
        unitListB.value = unitList.value;
        //重新定位到选中值
        if (saveBei.value) {
          ReFindTableIndex();
        } else {
          filteredUnits.value = unitList.value.slice(0, unitPageSize.value);
        }
      }
    });
};

/***筛选 */
const filterUnits = () => {
  if (searchUnitKeyword.value) {
    unitList.value = unitListB.value.filter(x => x.label.includes(searchUnitKeyword.value));
    filteredUnits.value = unitList.value.slice(0, unitPageSize.value);
    totalUnits.value = unitList.value.length;
  } else {
    filteredUnits.value = unitListB.value.slice(0, unitPageSize.value);
    totalUnits.value = unitListB.value.length;
    unitList.value = unitListB.value;
  }
};

/*****处理点击****/
async function handleUnitSelect(row: any) {
  //判断编译是否保存
  if (notice.value.typeCode != "" && notice.value.content != content.value) {
    ElMessageBox.confirm("您的编译尚未保存，是否放弃?", "温馨提示", {
      confirmButtonText: "放弃",
      cancelButtonText: "取消",
      type: "warning",
      draggable: true
    })
      .then(async () => {
        await selectValue(row);
      })
      .catch(() => {});
  } else {
    await selectValue(row);
  }
}
/*****选中赋值****/
async function selectValue(row: any) {
  // 先触发编辑器重建
  isDisabled.value = false;
  editorKey.value++;
  await nextTick(); // 确保DOM更新完成
  // 添加延迟确保编辑器实例化完成
  return new Promise<void>(() =>
    setTimeout(async () => {
      notice.value.typeCode = row.typeCode;
      notice.value.content = row.content;
      notice.value.label = row.label;
      content.value = row.content;
      notice.value.id = row.id;
    }, 100)
  );
}

/*****分页处理：一页多少条****/
const handleUnitSizeChange = (size: number) => {
  unitPageSize.value = size; //一页多少条
  if (unitPageSize.value >= unitList.value.length) {
    filteredUnits.value = unitList.value;
  } else {
    filteredUnits.value = unitList.value.slice(0, unitPageSize.value);
  }
};
/*****分页处理：第几页****/
const handleUnitCurrentChange = (page: number) => {
  unitCurrentPage.value = page; //第几页
  if (unitList.value.length > unitPageSize.value) {
    let start = (page - 1) * unitPageSize.value;
    let end = start + unitPageSize.value;
    end = end >= unitList.value.length ? unitList.value.length : end;
    filteredUnits.value = unitList.value.slice(start, end);
  } else {
    filteredUnits.value = unitList.value;
  }
};

/***保存数据 */
const saveBei = ref("");
/*****更新、新增****/
async function EditNotices() {
  await noticeApi
    .EditNotices(
      {
        Id: notice.value.id,
        TypeCode: notice.value.typeCode,
        Status: notice.value.status ? notice.value.status : "N",
        Content: content.value
      },
      notice.value.id != ""
    )
    .then(res => {
      if (res.code == "200") {
        ElMessage.success("保存成功");
        // notice.value.content = content.value;
        saveBei.value = notice.value.typeCode;
        fetchUnitList();
      } else {
        ElMessage.error("保存失败");
      }
    });
}

/***重新定位到选中值，配合saveBei使用 */
function ReFindTableIndex() {
  let targetIndex = unitListB.value.findIndex(x => x.typeCode === saveBei.value);
  let select_value = targetIndex !== -1 ? unitListB.value[targetIndex] : undefined;
  if (select_value == undefined || unitPageSize.value >= targetIndex + 1) {
    filteredUnits.value = unitList.value.slice(0, unitPageSize.value);
  } else {
    handleUnitCurrentChange(Math.ceil((targetIndex + 1) / unitPageSize.value));
  }
  if (select_value != undefined) {
    selectValue(select_value);
  }
}

/**
 * 修改状态
 * @param row  当前行数据
 */
async function editStatus(row: sysNotice.noticeTable) {
  const status = row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE; // 状态取反
  // console.log("row", row); return
  //不存在id
  if (!row.id) {
    if (row.typeCode == notice.value.typeCode) {
      row.status = status;
      if (row.typeCode == notice.value.typeCode) {
        notice.value.status = status;
      }
    }
    ElMessage.error("请完成内容输入并保持！");
    return;
  }

  noticeApi
    .UpdateNoticeStatus({
      id: row.id,
      status: status
    })
    .then(() => {
      // switchLoading.value = false;
      ElMessage.success("修改成功");
      row.status = status;
      // fetchUnitList();
    });
}
</script>

<style scoped lang="scss">
// .search-label {
//   margin: 0 10px;
//   font-weight: bold;
// }
.stickyTop {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #ffffff;
}
.stickyFooter {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  z-index: 1000;
  background-color: #ffffff;
}
.unit-list-container {
  cursor: pointer;
  .unit-card {
    height: 85vh;
  }
  .person-lists-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 80vh;
    .search-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;
      .header-actions {
        display: flex;
        gap: 10px;
        margin-left: auto;
      }
    }
    .person-card {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
      &.inactive-card {
        margin-bottom: 15px;
      }
      :deep(.el-card__body) {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: hidden;
      }
      .person-list {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: hidden;
        .el-table {
          flex: 1;
        }
      }
    }
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 16px;
      font-weight: bold;
    }
    .count-badge {
      padding: 2px 8px;
      font-size: 12px;
      color: white;
      background-color: #f56c6c;
      border-radius: 10px;
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }

  // 导入对话框样式
  // .import-dialog-content {
  //   min-height: 60%;

  //   .upload-area {
  //     display: flex;
  //     justify-content: center;
  //     padding: 20px;
  //   }

  //   .preview-area {
  //     .preview-header {
  //       display: flex;
  //       align-items: center;
  //       justify-content: space-between;
  //       margin-bottom: 15px;

  //       h3 {
  //         margin: 0;
  //       }

  //       .preview-actions {
  //         display: flex;
  //         gap: 10px;
  //       }
  //     }
  //   }
  // }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }
  ::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}
</style>
