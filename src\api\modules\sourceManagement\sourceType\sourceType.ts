/**
 * @description 号源类型接口
 */

import { moduleRequest } from "@/api/request";
import { ReqId, SourceType, TimeSlots } from "@/api/interface";
const http = moduleRequest("/admin/numbersource/");

const sourceTypeApi = {
  /** 获取号源类型列表 */
  page() {
    return http.post<SourceType.SourceTypeInfo[]>("getsourcetype", {});
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params: {}) {
    return http.post("edittimeslotbytype", params);
  },
  /** 号源类型详情 */
  getSourceTypeDetail(params: {}) {
    return http.post<SourceType.SourceTypeDetails[]>("gettimeslotbytype", params);
  }
};

export { sourceTypeApi };
