<template>
  <div class="table-box">
    <ProTable ref="proTable" title="号源列表" :columns="columns" :request-api="numberSourceApi.page" :init-param="initParam">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <s-button suffix="号源" @click="onOpen(FormOptEnum.ADD)" />
        <s-button
          type="danger"
          plain
          suffix="号源"
          :opt="FormOptEnum.DELETE"
          :disabled="!scope.isSelected"
          @click="onDelete(scope.selectedListIds, '删除所选号源')"
        />
      </template>
      <!-- 状态 -->
      <template #status="scope">
        <el-tag v-if="scope.row.status === CommonStatusEnum.ENABLE" type="success">{{
          dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status)
        }}</el-tag>
        <el-tag v-else type="danger">{{ dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status) }}</el-tag>
      </template>
      <!-- 操作 -->
      <template #operation="scope">
        <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)"> 编辑 </s-button>
        <s-button link :opt="FormOptEnum.DELETE" @click="onDelete([scope.row.id], `删除【${scope.row.name}】号源`)"> 删除 </s-button>
      </template>
    </ProTable>
    <!-- 新增/编辑表单 -->
    <Form ref="formRef" />
  </div>
</template>

<script setup lang="ts" name="SourceSetting">
import { numberSourceApi, numberSource } from "@/api";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import { FormOptEnum, CommonStatusEnum, SysDictEnum } from "@/enums";
import { useDictStore } from "@/stores/modules";
import Form from "./components/form.vue";

defineOptions({
  name: "SourceSetting"
});

// 字典仓库
const dictStore = useDictStore();

// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<numberSource.Page>({
  pageNum: 1,
  pageSize: 10
});

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

// 表格配置项
const columns: ColumnProps<numberSource.SourceInfo>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "name", label: "号源名称", search: { el: "input" } },
  { prop: "sourceDate", label: "号源日期", search: { el: "date-picker" } },
  { prop: "startTime", label: "开始时间" },
  { prop: "endTime", label: "结束时间" },
  { prop: "capacity", label: "号源容量" },
  { prop: "reserved", label: "已预约数量" },
  {
    prop: "status",
    label: "状态",
    width: 90,
    enum: statusOptions,
    search: { el: "select" }
  },
  { prop: "createTime", label: "创建时间" },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

// 获取表单组件实例
const formRef = ref();

// 打开表单
const onOpen = async (opt: FormOptEnum, row?: numberSource.SourceInfo) => {
  formRef.value.openDialog(opt, row);
};

// 删除
const onDelete = async (ids: string[] | number[], msg: string) => {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(numberSourceApi.delete, { ids: ids.map(id => ({ id })) }, msg);
  proTable.value?.refresh();
};
</script>

<style lang="scss" scoped>
.table-box {
  margin: 20px;
}
</style>
