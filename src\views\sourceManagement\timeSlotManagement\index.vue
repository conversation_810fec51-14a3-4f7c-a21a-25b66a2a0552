<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable
        ref="proTable"
        title="时段管理列表"
        :columns="columns"
        :data="tableData"
        @search="onSearch"
        @reset="onSearch"
        :pagination="false"
        :toolButton="['setting', 'search']"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <s-button suffix="时段" @click="onOpen(FormOptEnum.ADD)" />
          <s-button
            type="danger"
            plain
            suffix="时段"
            :opt="FormOptEnum.DELETE"
            :disabled="!scope.isSelected"
            @click="onDelete(scope.selectedListIds, '删除所选时段')"
          />
        </template>
        <!-- 状态 -->
        <template #timePeriod="scope">
          <div>{{ dictStore.dictTranslation(SysDictEnum.TIME_INTERVAL, scope.row.timePeriod) }}</div>
        </template>
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
            <s-button link :opt="FormOptEnum.DELETE" @click="onDelete([scope.row.id], `删除【${scope.row.timeSlotName}】`)" />
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script lang="ts" setup name="timeSlotManagement">
import { timeSlotsApi, TimeSlots } from "@/api";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { useHandleData } from "@/hooks/useHandleData";
import Form from "./components/form/index.vue";
import { useDictStore } from "@/stores/modules";

const dictStore = useDictStore(); //字典仓库
// 表格配置项
const columns: ColumnProps<TimeSlots.TimeSlotsInfo>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "searchKey", label: "时段名称", search: { el: "input" }, isShow: false },
  { prop: "timeSlotName", label: "时段名称" },
  { prop: "startTime", label: "开始时间" },
  { prop: "endTime", label: "结束时间" },
  { prop: "timePeriod", label: "时段范围" },
  { prop: "operation", label: "操作", width: 230, fixed: "right" }
];
// 表格数据
const tableData = ref<TimeSlots.TimeSlotsInfo[]>([]);
// 过滤后的数据
const filterTable = ref<TimeSlots.TimeSlotsInfo[]>([]);
// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);
// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

/**
 * 搜索
 */
async function onSearch() {
  const { searchKey } = proTable.value?.searchParam || {};
  tableData.value = filterTable.value.filter(item => {
    // 名称/编码搜索
    const keywordMatch = searchKey ? [item.timeSlotName].some(field => field?.toLowerCase().includes(searchKey.toLowerCase())) : true;

    return keywordMatch;
  });
}
/**
 * 获取时段列表
 */
async function getPage() {
  const { data } = await timeSlotsApi.page();
  tableData.value = data;
  filterTable.value = data;
}
/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | TimeSlots.TimeSlotsInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.clearSelection();
  getPage();
}

/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(timeSlotsApi.delete, { ids }, msg);
  RefreshTable();
}

onMounted(() => {
  getPage();
});
</script>

<style lang="scss" scoped></style>
