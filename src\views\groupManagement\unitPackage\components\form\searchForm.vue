<template>
  <div class="search-form">
    <el-form :model="searchForm" class="" ref="searchFormRef" label-suffix=" :">
      <el-form-item label="套餐名称" prop="searchKey">
        <el-input v-model="searchForm.searchKey" placeholder="请输入套餐名称" clearable />
      </el-form-item>
      <el-form-item label="套餐状态" prop="status">
        <el-select v-model="searchForm.status" placeholder="请选择套餐状态" clearable>
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="operation">
          <el-button :icon="Search" type="primary" @click="onSearch">搜索</el-button>
          <el-button :icon="Delete" @click="onReset">重置</el-button>
          <el-button :icon="CirclePlus" type="primary" @click="onOpen">新增套餐</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { SysDictEnum } from "@/enums";
import { useDictStore } from "@/stores/modules";
import { Delete, Search, CirclePlus } from "@element-plus/icons-vue";
import { FormInstance } from "element-plus";

/** 搜索表单 */
const searchForm = reactive({
  searchKey: "",
  status: ""
});
const dictStore = useDictStore(); //字典仓库
// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
/** 搜索表单ref */
const searchFormRef = ref<FormInstance>();

/** 定义组件对外暴露的方法 */
const emit = defineEmits(["search", "open"]);
/** 搜索 */
function onSearch() {
  emit("search");
}
/** 重置 */
function onReset() {
  searchFormRef.value?.resetFields();
  emit("search");
}
/** 打开 */
function onOpen() {
  emit("open");
}
defineExpose({
  searchForm,
  onSearch,
  onReset
});
</script>

<style lang="scss" scoped>
.search-form {
  .operation {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
