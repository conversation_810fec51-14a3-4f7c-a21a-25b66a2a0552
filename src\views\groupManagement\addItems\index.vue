<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, OfficeBuilding, Calendar, Document } from "@element-plus/icons-vue";
import type { UnitPackage, PackageItem, SearchForm, Pagination, CompanyTimesUI } from "./interface";
import { companyManagementApi } from "@/api/modules";
import { individualPackageApi } from "@/api/modules";
import { usePackages } from "./models/usePackages";
import { usePackageQuery } from "./models/usePackageQuery";
import { useCompanys } from "./models/useCompanys";
import { useCompanyTimes } from "./models/useCompanyTimes";
import { usePagination } from "./models/usePagination";
import { IndividualPackage } from "@/api/interface";
import { useDictStore } from "@/stores/modules";
import { SysDictEnum } from "@/enums";

const { init: initPackages, packages, searchPackages } = usePackages();
const { packageQuery } = usePackageQuery();
const { companys, searchCompanys } = useCompanys();
const { companyTimes, getCompanyTimes, init: initCompanyTimes } = useCompanyTimes();
const { pagination } = usePagination();

const dictStore = useDictStore(); //字典仓库
// 项目分类选项
const projectClsOptions = dictStore.getDictList(SysDictEnum.PROJECT_CLS);

// 表格数据
const loading = ref(false);

// 可用体检项目（用于详情展示）
const availableItems = ref<PackageItem[]>([]);

// 单位选择相关
const unitLoading = ref(false);

// 选中的套餐和详情数据
const selectedPackage = ref<UnitPackage | null>(null);
const activeTab = ref("basic");
const basicItems = ref<PackageItem[]>([]);
const additionalItems = ref<PackageItem[]>([]);
const packageItems = ref<any[]>([]);

// 体检项目相关数据
const combinationData = ref<IndividualPackage.CombsByItemCls[]>([]);
const filteredCombination = ref<IndividualPackage.CombsByItemCls[]>([]);
const selectedCombination = ref<string[]>([]);
const searchCombination = ref("");
const searchCombinationOption = ref("-1");

// 计算项目分类选项
const projectClsOptionsMap = computed(() => {
    return [{ value: "-1", label: "全部项目分类" }, ...projectClsOptions];
});

// 计算总价
const totalPrice = computed(() => {
    const codes = selectedCombination.value;
    const allCombs = combinationData.value.flatMap(item => item.combData);
    const total = allCombs.filter(comb => codes.includes(comb.combCode)).reduce((sum, item) => sum + item.price, 0);
    return Number(total.toFixed(2));
});

// 计算已选择组合的数据
const selectedCombinationData = computed(() => {
    const codes = selectedCombination.value;
    const allCombs = combinationData.value.flatMap(item => item.combData);
    return allCombs.filter(comb => codes.includes(comb.combCode));
});

onMounted(() => {
    initDate();
});

// 初始化数据
const initDate = async () => {
    //初始化默认单位信息
    await searchCompanys("");
    packageQuery.value.companyCode = companys.value[0].companyCode;

    //初始化默认单位信息
    companyTimes.value = await initCompanyTimes(packageQuery.value.companyCode);
    packageQuery.value.companyTimes = companyTimes.value[0].companyTimes;

    const { companyCode } = packageQuery.value;

    //初始化单位套餐信息
    await initPackages(companyCode, packageQuery.value.companyTimes);
    pagination.value.total = packages.value.length;

    // 初始化体检项目数据
    await getCombsByItemCls();
};

// 搜索套餐列表
const fetchPackageList = async (companyCode: string, companyTimes: number) => {
    packages.value = [];
    packages.value = await searchPackages(companyCode, companyTimes);
    pagination.value.total = packages.value.length;
};

// 点击选择单位触发默认数据
const changeUnit = async (companyCode: string) => {
    packages.value = [];

    const companyTimesData = await getCompanyTimes(companyCode);
    pagination.value.total = 0;
    const [firstTimes] = companyTimesData;
    packageQuery.value.companyTimes = firstTimes?.companyTimes ?? null;
    if (!!firstTimes?.companyTimes) {
        packages.value = await searchPackages(companyCode, companyTimesData[0]?.companyTimes);
        pagination.value.total = packages.value.length;
    }
};

// 点击行选择套餐
const handleRowClick = (row: any) => {
    selectedPackage.value = row;
    fetchPackageDetails(row.clusCode);
};

// 获取套餐详情
const fetchPackageDetails = async (clusCode: string) => {
    try {
        // 模拟API调用获取基础套餐详情
        const { data } = await companyManagementApi.getCompanyClusterComb(clusCode);
        basicItems.value = data;
    } catch (error) {
        console.error("获取套餐详情失败:", error);
        ElMessage.error("获取套餐详情失败");
    }
};

// 获取项目组合
const getCombsByItemCls = async () => {
    try {
        const { data } = await individualPackageApi.getCombsByItemCls();
        filteredCombination.value = data;
        combinationData.value = data;
    } catch (error) {
        console.error("获取体检项目失败:", error);
        ElMessage.error("获取体检项目失败");
    }
};

// 搜索组合
const onSearchCombination = () => {
    // 先根据分类过滤基础数据
    const baseData =
        searchCombinationOption.value === "-1"
            ? combinationData.value
            : combinationData.value.filter(item => item.clsCode === searchCombinationOption.value);

    // 再根据搜索词过滤组合名称
    filteredCombination.value = baseData
        .map(item => ({
            ...item,
            combData: item.combData.filter(comb => comb.combName.includes(searchCombination.value))
        }))
        .filter(item => item.combData.length > 0);
};

// 移除组合
const onRemoveCombination = (row: IndividualPackage.CombinationData) => {
    selectedCombination.value = selectedCombination.value.filter(item => item !== row.combCode);
};

// 保存加项到套餐
const saveAdditionalItems = async () => {
    if (!selectedPackage.value) {
        ElMessage.warning("请先选择套餐");
        return;
    }

    if (selectedCombination.value.length === 0) {
        ElMessage.warning("请选择要添加的体检项目");
        return;
    }

    try {
        // 这里应该调用保存加项的API
        ElMessage.success("加项保存成功");
        // 清空选择
        selectedCombination.value = [];
    } catch (error) {
        console.error("保存加项失败:", error);
        ElMessage.error("保存加项失败");
    }
};

// 分页处理
const handleSizeChange = (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.currentPage = 1;
};

const handleCurrentChange = (page: number) => {
    pagination.value.currentPage = page;
};
</script>

<template>
    <div class="unit-package-container">
        <!-- 左右布局 -->
        <el-row :gutter="20" class="main-content">
            <!-- 左侧：套餐列表 -->
            <el-col :span="6">
                <el-card class="package-list-card" shadow="never">
                    <!-- 查询表单 -->
                    <div class="search-form">
                        <el-form :model="packageQuery" size="small" label-width="70px" class="search-form-content">
                            <el-form-item label="单位名称" class="form-item">
                                <el-select v-model="packageQuery.companyCode" placeholder="请选择或搜索单位" clearable
                                    filterable remote @change="changeUnit(packageQuery.companyCode)"
                                    :loading="unitLoading" style="width: 100%" class="custom-select" size="default">
                                    <template #prefix>
                                        <el-icon class="input-icon">
                                            <OfficeBuilding />
                                        </el-icon>
                                    </template>
                                    <el-option v-for="unit in companys" :key="unit.companyCode"
                                        :label="unit.companyName" :value="unit.companyCode" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="体检次数" class="form-item">
                                <el-select v-model="packageQuery.companyTimes" placeholder="请选择体检次数" clearable
                                    style="width: 100%" class="custom-select"
                                    @change="fetchPackageList(packageQuery.companyCode, packageQuery.companyTimes)"
                                    size="default">
                                    <template #prefix>
                                        <el-icon class="input-icon">
                                            <Calendar />
                                        </el-icon>
                                    </template>
                                    <el-option v-for="item in companyTimes" :key="item.companyTimes"
                                        :label="`第${item.companyTimes}次体检`" :value="item.companyTimes" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="套餐名称" class="form-item">
                                <el-input v-model="packageQuery.packageName" placeholder="请输入套餐名称关键词" clearable
                                    style="width: 100%" class="custom-input" size="default">
                                    <template #prefix>
                                        <el-icon class="input-icon">
                                            <Document />
                                        </el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- 套餐表格 -->
                    <el-table :data="packages" border stripe v-loading="loading" :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                    }" @row-click="handleRowClick" :highlight-current-row="true" height="60vh">
                        <el-table-column prop="clusCode" label="套餐编码" width="120" />
                        <el-table-column prop="clusName" label="套餐名称" show-overflow-tooltip />
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-container">
                        <el-pagination v-model:current-page="pagination.currentPage"
                            v-model:page-size="pagination.pageSize" :page-sizes="[10, 20, 50]"
                            layout="total, sizes, prev, pager, next" :total="pagination.total" background small
                            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
                    </div>
                </el-card>
            </el-col>

            <!-- 右侧：套餐详情和加项设置 -->
            <el-col :span="18">
                <el-card class="package-detail-card" shadow="never" v-if="selectedPackage">
                    <template #header>
                        <div class="card-header">
                            <span class="title">套餐加项设置 - {{ selectedPackage.clusName }}</span>
                        </div>
                    </template>

                    <div class="add-items-container">
                        <!-- 搜索和筛选区域 -->
                        <div class="search-section">
                            <el-input v-model="searchCombination" placeholder="请输入组合名称" style="width: 500px">
                                <template #prepend>
                                    <el-select v-model="searchCombinationOption" placeholder="" style="width: 200px"
                                        @change="onSearchCombination">
                                        <el-option :label="item.label" :value="item.value"
                                            v-for="(item, index) in projectClsOptionsMap" :key="index" />
                                    </el-select>
                                </template>
                                <template #append>
                                    <el-button :icon="Search" @click="onSearchCombination" />
                                </template>
                            </el-input>
                            <!-- 操作按钮 -->
                            <div class="action-buttons">
                                <el-button type="primary" @click="saveAdditionalItems"
                                    :disabled="selectedCombination.length === 0">
                                    保存加项到套餐 </el-button>
                                <el-button @click="selectedCombination = []">清空选择</el-button>
                            </div>
                        </div>

                        <!-- 体检项目选择区域 -->
                        <div class="items-selection-container">
                            <div class="items-list-section">
                                <div class="section-title">可选体检项目</div>
                                <div class="combination-container">
                                    <div v-for="(item, index) in filteredCombination" :key="index"
                                        class="combination-item">
                                        <div class="combination-title">
                                            <el-check-tag checked>{{ item.clsName }}</el-check-tag>
                                        </div>
                                        <el-checkbox-group v-model="selectedCombination"
                                            class="combination-checkbox-group">
                                            <el-checkbox :label="comb.combCode" border class="combination-checkbox"
                                                v-for="(comb, combIndex) in item.combData" :key="combIndex">
                                                {{ `${comb.combName}　　￥${comb.price}` }}
                                            </el-checkbox>
                                        </el-checkbox-group>
                                    </div>
                                    <el-empty v-if="filteredCombination.length === 0" description="暂无数据" />
                                </div>
                            </div>

                            <div class="selected-items-section">
                                <div class="selected-header">
                                    <span class="selected-title">已选择项目</span>
                                    <span class="selected-count">共 {{ selectedCombination.length }} 项</span>
                                    <span class="selected-price">总价：{{ totalPrice.toFixed(2) }}元</span>
                                </div>
                                <el-table :data="selectedCombinationData" style="width: 100%; overflow-y: hidden"
                                    height="60vh" max-height="60vh" border>
                                    <el-table-column prop="combCode" label="组合编码" width="120" />
                                    <el-table-column prop="combName" label="组合名称" />
                                    <el-table-column prop="price" label="价格" width="100" />
                                    <el-table-column fixed="right" label="操作" width="110">
                                        <template #default="scope">
                                            <el-button link type="danger"
                                                @click="onRemoveCombination(scope.row)">移除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </el-card>

                <!-- 未选择套餐时的提示 -->
                <el-card class="empty-card" shadow="never" v-else>
                    <el-empty description="请选择左侧套餐查看详情" />
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<style scoped lang="scss">
.unit-package-container {
    .search-card {
        margin-bottom: 20px;
    }
    .main-content {
        height: 80vh;
    }
    .package-list-card {
        height: 100%;
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-size: 16px;
                font-weight: bold;
                color: #303133;
            }
        }
        .search-form {
            margin-bottom: 15px;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgb(102 126 234 / 15%);
            .search-form-header {
                display: flex;
                align-items: center;
                padding: 15px 20px;
                background: rgb(255 255 255 / 10%);
                backdrop-filter: blur(10px);
                border-bottom: 1px solid rgb(255 255 255 / 10%);
                .search-icon {
                    margin-right: 8px;
                    font-size: 18px;
                    color: #ffffff;
                }
                .search-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #ffffff;
                    letter-spacing: 0.5px;
                }
            }
            .search-form-content {
                padding: 5px;
                background: rgb(255 255 255 / 95%);
                backdrop-filter: blur(10px);
                :deep(.el-form-item) {
                    margin-bottom: 16px;
                    &.form-buttons {
                        margin-top: 8px;
                        margin-bottom: 0;
                        text-align: center;
                    }
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
                :deep(.el-form-item__label) {
                    font-size: 13px;
                    font-weight: 500;
                    line-height: 32px;
                    color: #606266;
                }
                :deep(.custom-select) {
                    .el-input__wrapper {
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
                        transition: all 0.3s ease;
                        &:hover {
                            box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
                        }
                        &.is-focus {
                            box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
                        }
                    }
                    .input-icon {
                        font-size: 14px;
                        color: #667eea;
                    }
                }
                :deep(.custom-input) {
                    .el-input__wrapper {
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
                        transition: all 0.3s ease;
                        &:hover {
                            box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
                        }
                        &.is-focus {
                            box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
                        }
                    }
                    .input-icon {
                        font-size: 14px;
                        color: #667eea;
                    }
                }
                .search-btn {
                    padding: 8px 20px;
                    margin-right: 12px;
                    font-weight: 500;
                    border: none;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
                    transition: all 0.3s ease;
                    &:hover {
                        box-shadow: 0 6px 16px rgb(102 126 234 / 40%);
                        transform: translateY(-2px);
                    }
                    &:active {
                        transform: translateY(0);
                    }
                }
                .reset-btn {
                    padding: 8px 20px;
                    font-weight: 500;
                    color: #606266;
                    background: #ffffff;
                    border: 1px solid #dcdfe6;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    &:hover {
                        background: #f5f7fa;
                        border-color: #c0c4cc;
                        box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
                        transform: translateY(-1px);
                    }
                    &:active {
                        transform: translateY(0);
                    }
                }
            }
        }
        .pagination-container {
            display: flex;
            justify-content: center;
            padding: 10px 0;
            margin-top: 15px;
            border-top: 1px solid #e4e7ed;
        }
        :deep(.el-table) {
            .el-table__row {
                cursor: pointer;
                &:hover {
                    background-color: #f5f7fa;
                }
            }
            .current-row {
                background-color: #ecf5ff !important;
                &:hover {
                    background-color: #ecf5ff !important;
                }
            }
        }
    }
    .package-detail-card {
        height: 100%;
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-size: 16px;
                font-weight: bold;
                color: #303133;
            }
            .header-actions {
                display: flex;
                gap: 10px;
            }
        }
        .add-items-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: calc(100% - 60px);
            .search-section {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                padding: 10px 0;
            }
            .items-selection-container {
                display: grid;
                flex: 1;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                min-height: 0;
                .items-list-section {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    height: 70vh;
                    overflow-y: hidden;
                    .section-title {
                        padding: 8px 0;
                        font-size: 14px;
                        font-weight: bold;
                        color: #303133;
                    }
                    .combination-container {
                        display: flex;
                        flex: 1;
                        flex-direction: column;
                        gap: 10px;
                        padding: 10px;
                        overflow: auto;
                        border: 1px solid var(--el-border-color);
                        border-radius: 4px;
                        .combination-item {
                            display: flex;
                            flex-direction: column;
                            gap: 10px;
                        }
                        .combination-title {
                            padding: 5px 0;
                            font-size: 14px;
                            font-weight: bold;
                            color: #409eff;
                            border-bottom: 1px solid #ebeef5;
                        }
                        .combination-checkbox-group {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 10px;
                            margin-left: 20px;
                        }
                        .combination-checkbox {
                            margin-bottom: 10px;
                        }
                    }
                }
                .selected-items-section {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    .selected-header {
                        display: grid;
                        grid-template-columns: 86px 1fr 1fr;
                        gap: 10px;
                        align-items: center;
                        padding: 6px 8px;
                        font-size: 14px;
                        font-weight: bold;
                        background-color: var(--el-fill-color-light);
                        border-radius: 4px;
                        .selected-count {
                            font-weight: normal;
                        }
                        .selected-price {
                            color: var(--el-color-danger);
                            text-align: right;
                        }
                    }
                }
            }
            .action-buttons {
                display: flex;
                gap: 15px;
                justify-content: center;
                padding: 0 15px;
                border-top: 1px solid #e4e7ed;
            }
        }
        :deep(.el-card__body) {
            height: calc(100% - 60px);
            overflow-y: auto;
        }
    }
    .empty-card {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        :deep(.el-card__body) {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
    }
}

// 全局样式调整
:deep(.el-table) {
    .el-table__header-wrapper {
        .el-table__header {
            th {
                font-weight: bold;
                color: #303133;
                background-color: #f5f7fa;
            }
        }
    }
}
</style>
