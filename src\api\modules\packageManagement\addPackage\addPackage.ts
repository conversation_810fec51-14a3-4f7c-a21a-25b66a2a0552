/**
 * @description 套餐加项包接口
 */
import { AddPackage } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/admin/basiccode/");

const addPackageApi = {
  /** 套餐加项包数据 */
  page(params: {}) {
    return http.post<AddPackage.AddPackageInfo[]>("getaddpackage", params);
  },
  /** 新增/修改套餐加项包 */
  submitForm(params: {}) {
    return http.post("saveaddpackage", params);
  },
  /** 获取套餐详情 */
  detail(params: { addPkgCode: string }) {
    return http.post<AddPackage.AddPackageDetail[]>("getaddpackagedetail", params);
  },
  /** 删除套餐加项包 */
  delete(params: {}) {
    return http.post("deleteaddpackage", params);
  }
};

export { addPackageApi };
