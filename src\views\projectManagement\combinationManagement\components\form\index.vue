<template>
  <div>
    <form-container v-model="visible" :title="`${combinationManagementProps.opt}组合`" form-size="600px" @close="onClose">
      <el-form
        class="form"
        ref="combinationManagementFormRef"
        :rules="rules"
        :disabled="combinationManagementProps.disabled"
        :model="combinationManagementProps.record"
        :hide-required-asterisk="combinationManagementProps.disabled"
        label-width="auto"
        label-suffix=" :"
      >
        <s-form-item label="组合编码" prop="combCode">
          <s-input v-model="combinationManagementProps.record.combCode" placeholder="请输入组合编码" disabled></s-input>
        </s-form-item>
        <s-form-item label="组合名称" prop="combName">
          <s-input v-model="combinationManagementProps.record.combName" placeholder="请输入组合名称" disabled></s-input>
        </s-form-item>
        <s-form-item label="适用性别" prop="gender">
          <s-radio-group v-model="combinationManagementProps.record.gender" :options="genderOptions" disabled></s-radio-group>
        </s-form-item>
        <s-form-item label="价格" prop="price">
          <el-input-number v-model="combinationManagementProps.record.price" :precision="2" :min="0" :controls="false" disabled />
        </s-form-item>
        <s-form-item label="状态" prop="status">
          <s-radio-group v-model="combinationManagementProps.record.status" :options="statusOptions" disabled></s-radio-group>
        </s-form-item>
        <s-form-item label="注意事项" prop="attention">
          <el-input
            v-model="combinationManagementProps.record.attention"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            placeholder="请输入注意事项"
            disabled
          />
        </s-form-item>
        <el-divider content-position="left">可编辑项</el-divider>
        <s-form-item label="项目分类" prop="clsCode">
          <s-select v-model="combinationManagementProps.record.clsCode" :options="projectClsOptions"></s-select>
        </s-form-item>
        <s-form-item label="是否独立放号" prop="isPriority">
          <s-radio-group v-model="combinationManagementProps.record.isPriority" :options="isPriorityOptions"></s-radio-group>
        </s-form-item>
        <s-form-item label="描述" prop="description">
          <el-input
            v-model="combinationManagementProps.record.description"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            placeholder="请输入描述"
          />
        </s-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!combinationManagementProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { CombinationManagement, combinationManagementApi } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
// 性别选项
const genderOptions = dictStore.getDictList(SysDictEnum.GENDER);
// 是否独立放号选项
const isPriorityOptions = dictStore.getDictList(SysDictEnum.YES_NO);
// 项目分类选项
const projectClsOptions = dictStore.getDictList(SysDictEnum.PROJECT_CLS);
// 表单参数
const combinationManagementProps = reactive<FormProps.Base<CombinationManagement.CombinationInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  clsCode: [required("请选择项目分类")],
  isPriority: [required("请选择是否独立放号")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
async function onOpen(props: FormProps.Base<CombinationManagement.CombinationInfo>) {
  Object.assign(combinationManagementProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    combinationManagementProps.record = {
      combCode: "",
      combName: "",
      clsCode: projectClsOptions[0].value,
      gender: genderOptions[0].value,
      price: 0,
      status: statusOptions[0].value,
      isPriority: isPriorityOptions[0].value,
      description: "",
      attention: ""
    };
  }
  visible.value = true; //显示表单
  if (props.opt == FormOptEnum.EDIT) {
    combinationManagementProps.record = JSON.parse(JSON.stringify(props.record));
  }
}

// 提交数据（新增/编辑）
const combinationManagementFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  combinationManagementFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    await combinationManagementApi
      .submitForm(combinationManagementProps.record, combinationManagementProps.record.id != undefined)
      .then(() => {
        ElMessage.success("保存成功");
        combinationManagementProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container {
  .form {
    margin-top: 20px;
  }
  :deep(.el-dialog__body) {
    margin-top: 0;
  }
  :deep(.el-drawer__body) {
    margin-top: -20px;
  }
  :deep(.el-input-number) {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding: 0;
    .el-input__inner {
      font-size: 1.1em;
      text-align: left;
    }
  }
}
</style>
