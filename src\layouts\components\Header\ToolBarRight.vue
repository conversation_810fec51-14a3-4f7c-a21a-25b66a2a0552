<template>
  <div class="tool-bar-ri">
    <el-dropdown trigger="click">
      <el-button>{{ orgInfo == null ? "请选择院区" : orgInfo?.orgName + "[" + orgInfo?.areaName + "]" }}</el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in userStore.orgList" @click="openDialog(item)">
            <el-icon> <School /> </el-icon>{{ item.orgName + "[" + item.areaName + "]" }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <div class="header-icon">
      <AssemblySize id="assemblySize" />
      <SearchMenu id="searchMenu" />
      <ThemeSetting id="themeSetting" />
      <!-- <Message id="message" /> -->
      <Fullscreen id="fullscreen" />
      <!-- <ChangeModule id="changeModule" /> -->
    </div>
    <span class="username">{{ username }}</span>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { SysOrg } from "@/api/interface";
import { useUserStore, useConfigStore } from "@/stores/modules";
import AssemblySize from "./components/AssemblySize.vue";
import ThemeSetting from "./components/ThemeSetting.vue";
// import Message from "./components/Message.vue";
import Fullscreen from "./components/Fullscreen.vue";
import Avatar from "./components/Avatar.vue";
// import ChangeModule from "./components/ChangeModule.vue";
import SearchMenu from "./components/SearchMenu.vue";

const userStore = useUserStore();
const configStore = useConfigStore();

const username = computed(() => userStore.userInfo?.name);
const orgInfo = computed(() => configStore.orgInfo);
const openDialog = (item: SysOrg.SysOrgInfo) => {
  configStore.setOrgInfo(item);
};
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 25px;

  /* stylelint-disable-next-line rule-empty-line-before */
  .header-icon {
    display: flex;
    align-items: center;

    /* stylelint-disable-next-line rule-empty-line-before */
    & > * {
      margin-left: 17px;
      color: var(--el-header-text-color);
    }
  }

  /* stylelint-disable-next-line rule-empty-line-before */
  .username {
    margin: 0 16px;
    font-size: 15px;
    color: var(--el-header-text-color);
  }
}
</style>
