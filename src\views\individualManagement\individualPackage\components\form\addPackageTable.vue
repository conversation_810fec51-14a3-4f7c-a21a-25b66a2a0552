<template>
  <div class="add-package-table">
    <div class="table-item">
      <el-input v-model="searchValue" placeholder="请输入编码/名称">
        <template #append>
          <el-button :icon="Search" @click="onSearch" />
        </template>
      </el-input>
      <el-table :data="filteredAddPackage" style="width: 100%" height="100%" border>
        <el-table-column prop="addPackageCode" label="加项包编码" />
        <el-table-column prop="addPackageName" label="加项包名称" />
        <el-table-column prop="price" label="价格" />
        <el-table-column prop="gender" label="适用性别">
          <template #default="scope">
            <div>{{ dictStore.dictTranslation(SysDictEnum.GENDER, scope.row.gender) }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="140">
          <template #default="scope">
            <el-button link type="primary" @click="onAdd(scope.row)">添加</el-button>
            <el-button link type="success" @click="onDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="table-item">
      <div class="selected-header">
        <span class="selected-title">已选择加项包</span>
        <span class="selected-count">共 {{ selectedAddPackageData.length }} 个</span>
        <span class="selected-price">总价：{{ totalPrice.toFixed(2) }}元</span>
      </div>
      <el-table :data="selectedAddPackageData" style="width: 100%" height="100%" border>
        <el-table-column prop="addPackageCode" label="加项包编码" />
        <el-table-column prop="addPackageName" label="加项包名称" />
        <el-table-column prop="price" label="价格" />
        <el-table-column prop="gender" label="适用性别">
          <template #default="scope">
            <div>{{ dictStore.dictTranslation(SysDictEnum.GENDER, scope.row.gender) }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="110">
          <template #default="scope">
            <el-button link type="danger" @click="onRemove(scope.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-model="dialogVisible" title="查看详情" width="500px">
      <div class="detail-container">
        <div class="detail-label">加项包名称:</div>
        <div>{{ addPackageInfo.addPackageName }}</div>
        <div class="detail-label">价格:</div>
        <div>￥{{ addPackageInfo.price }}</div>
        <div class="detail-label">适用性别:</div>
        <div>{{ dictStore.dictTranslation(SysDictEnum.GENDER, addPackageInfo.gender) }}</div>
        <div class="detail-label">包含组合:</div>
        <div>{{ addPackageInfo.combData }}</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { addPackageApi, AddPackage } from "@/api";
import { Search } from "@element-plus/icons-vue";
import { useDictStore } from "@/stores/modules";
import { SysDictEnum } from "@/enums";

const dictStore = useDictStore(); //字典仓库
/** 搜索关键字 */
const searchValue = ref("");
/** 加项包数据 */
const addPackageData = ref<AddPackage.AddPackageInfo[]>([]);
/** 过滤后的加项包数据 */
const filteredAddPackage = ref<AddPackage.AddPackageInfo[]>([]);
/** 选择的加项包 */
const selectedAddPackageData = ref<AddPackage.AddPackageInfo[]>([]);
/** 详情弹窗 */
const dialogVisible = ref(false);
/** 加项包详情 */
const addPackageInfo = ref({
  addPackageName: "",
  gender: "",
  price: 0,
  combData: ""
});
// 计算总价
const totalPrice = computed(() => {
  const total = selectedAddPackageData.value.reduce((sum, item) => sum + item.price, 0);
  return Number(total.toFixed(2));
});

/** 搜索加项包 */
function onSearch() {
  const keyword = searchValue.value.trim().toLowerCase();
  if (keyword) {
    filteredAddPackage.value = addPackageData.value.filter(
      item => item.addPackageName.toLowerCase().includes(keyword) || item.addPackageCode.toLowerCase().includes(keyword)
    );
  } else {
    filteredAddPackage.value = addPackageData.value;
  }
}

// 查看详情
async function onDetail(row: AddPackage.AddPackageInfo) {
  const { data } = await addPackageApi.detail({ addPkgCode: row.addPackageCode });
  addPackageInfo.value = {
    addPackageName: row.addPackageName,
    gender: row.gender,
    price: row.price,
    combData: data.map(item => item.combName).join("，")
  };
  dialogVisible.value = true;
}
// 添加加项包
function onAdd(row: AddPackage.AddPackageInfo) {
  if (selectedAddPackageData.value.some(item => item.addPackageCode === row.addPackageCode)) {
    ElMessage.warning("已存在该加项包");
    return;
  }
  const addPackageCode = filteredAddPackage.value.some(item => item.addPackageCode === row.addPackageCode);
  if (addPackageCode) {
    selectedAddPackageData.value.push(row);
  }
}
// 移除加项包
function onRemove(row: AddPackage.AddPackageInfo) {
  const index = selectedAddPackageData.value.findIndex(item => item.addPackageCode === row.addPackageCode);
  if (index !== -1) {
    selectedAddPackageData.value.splice(index, 1);
  }
}
/*** 获取加项包列表 */
async function getAddPackageList() {
  const { data } = await addPackageApi.page({
    searchKey: searchValue.value || "",
    gender: "",
    isCompany: "N",
    companyCode: ""
  });
  addPackageData.value = data;
  filteredAddPackage.value = data;
}
defineExpose({
  searchValue,
  addPackageData,
  filteredAddPackage,
  selectedAddPackageData,
  getAddPackageList
});
</script>

<style lang="scss" scoped>
.add-package-table {
  display: grid;
  grid-template-rows: 40vh;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  .table-item {
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
    .selected-header {
      display: grid;
      grid-template-columns: 98px 1fr 1fr;
      gap: 10px;
      align-items: center;
      padding: 6px 8px;
      font-size: 14px;
      font-weight: bold;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      .selected-count {
        font-weight: normal;
      }
      .selected-price {
        color: var(--el-color-danger);
        text-align: right;
      }
    }
  }
  .detail-container {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 10px;
    padding: 10px;
    .detail-label {
      font-weight: bold;
      text-align: right;
    }
  }
}
</style>
