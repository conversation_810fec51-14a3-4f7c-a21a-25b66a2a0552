/*
 * @Author: Reaper
 * @Date: 2025-06-06 15:20:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-09 09:59:24
 * @Description: 验证证件号码
 */
export type IdType = "居民身份证" | "护照" | "军官证" | "港澳居民通行证" | "台湾居民通行证";

export enum IdTypeEnum {
  居民身份证 = 1,
  护照 = 2,
  军官证 = 3,
  港澳居民通行证 = 4,
  台湾居民通行证 = 5
}

export class IdNumberTypeVerification {
  static validateIdNumber(idNumber: string, idType: IdType): boolean {
    if (!idNumber || typeof idNumber !== "string") {
      return false;
    }

    // 去除空格并转为大写
    const cleaned = idNumber.trim().toUpperCase();

    switch (idType) {
      case "居民身份证": // 居民身份证
        return validateChineseIdCard(cleaned);

      case "护照": // 护照
        return validatePassport(cleaned);

      case "军官证": // 军官证
        return validateOfficerCard(cleaned);

      case "港澳居民通行证": // 港澳居民通行证
        return validateHkMacauPass(cleaned);

      case "台湾居民通行证": // 台湾居民通行证
        return validateTaiwanPass(cleaned);

      default:
        return false;
    }
  }
}



// 验证居民身份证
function validateChineseIdCard(id: string): boolean {
  // 基本格式校验
  const reg = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
  if (!reg.test(id)) {
    return false;
  }

  // 校验码验证
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const checkCodes = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];

  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += parseInt(id.charAt(i)) * weights[i];
  }

  const mod = sum % 11;
  return id.charAt(17) === checkCodes[mod];
}

// 验证护照
function validatePassport(passport: string): boolean {
  // 普通护照格式校验
  // 因各国护照格式不同，这里仅做简单格式校验
  const reg = /^[a-zA-Z0-9<]{5,20}$/;
  return reg.test(passport);
}

// 验证军官证
function validateOfficerCard(officer: string): boolean {
  // 军官证格式校验
  const reg = /^[\u4e00-\u9fa5a-zA-Z0-9]{4,20}$/;
  return reg.test(officer);
}

// 验证港澳居民通行证
function validateHkMacauPass(pass: string): boolean {
  // 港澳居民来往内地通行证格式校验
  const reg = /^[HMhm]{1}([0-9]{10}|[0-9]{8})$/;
  return reg.test(pass);
}

// 验证台湾居民通行证
function validateTaiwanPass(pass: string): boolean {
  // 台湾居民来往大陆通行证格式校验
  const reg = /^[0-9]{8}$|^[0-9]{10}$/;
  return reg.test(pass);
}
