/**
 * @description 互斥组合接口
 */
import { CombinationMutuallyExclusive } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/admin/basiccode/");

const combinationMutuallyExclusiveApi = {
  /** 互斥组合分页 */
  page() {
    return http.post<CombinationMutuallyExclusive.CombinationMutuallyExclusiveInfo[]>("getmutexcombs", {});
  },
  /** 新增/修改互斥组合 */
  submitForm(params: {}) {
    return http.post("savemutexcombdetail", params);
  },
  /** 删除互斥组合 */
  delete(params: {}) {
    return http.post("deletemutexcomb", params);
  },
  /** 互斥组合详情 */
  detail(params: {}) {
    return http.post<CombinationMutuallyExclusive.CombinationMutuallyExclusiveDetailInfo[]>("getmutexcombdetail", {}, { params });
  }
};

export { combinationMutuallyExclusiveApi };
