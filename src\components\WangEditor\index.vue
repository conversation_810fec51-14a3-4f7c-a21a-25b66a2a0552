<template>
  <div :class="['editor-box', self_disabled ? 'editor-disabled' : '']">
    <!-- <Editor v-model="valueHtml" class="editor-content" :style="{ height }" :mode="mode" :default-config="editorConfig"
      @on-created="handleCreated" @on-blur="handleBlur">
    </Editor> -->
    <div style="display: flex">
      <div class="look_box">
        <div class="phone">
          <div class="phone_shadow">
            <div class="nav">
              <div style="padding-top: 5px; padding-left: 5px; font-size: 15px">
                {{ showData() }}
              </div>
              <div class="icons">
                <span style="margin-right: 5px; font-size: 22px">...</span>
                <span style="margin-right: 7px">
                  <svg
                    t="1701753286800"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="1539"
                    width="18"
                    height="18"
                  >
                    <path
                      d="M42.666667 384l85.333333 85.333333c212.053333-212.053333 555.946667-212.053333 768 0l85.333333-85.333333C722.133333 124.8 301.866667 124.8 42.666667 384z m341.333333 341.333333l128 128 128-128c-70.613333-70.613333-185.386667-70.613333-256 0z m-170.666667-170.666666l85.333334 85.333333c117.76-117.76 308.906667-117.76 426.666666 0l85.333334-85.333333c-164.906667-164.906667-432.426667-164.906667-597.333334 0z"
                      p-id="1540"
                      fill="#2d4247"
                    />
                  </svg>
                </span>
                <span>
                  <svg
                    t="1701754878961"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="6536"
                    width="23"
                    height="23"
                  >
                    <path
                      d="M144.700101 684.994006l535.580045 0L680.280146 359.237781 144.700101 359.237781 144.700101 684.994006 144.700101 684.994006zM918.373823 440.680675l0-81.442894c0-44.791136-36.649711-81.437777-81.437777-81.437777l-692.235944 0c-44.791136 0-81.437777 36.646642-81.437777 81.437777L63.262324 684.994006c0 44.791136 36.646642 81.442894 81.437777 81.442894l692.235944 0c44.788066 0 81.437777-36.650735 81.437777-81.442894l0-81.437777c22.396079 0 40.7194-18.322297 40.7194-40.7194l0-81.436754C959.093223 459.003995 940.769902 440.680675 918.373823 440.680675L918.373823 440.680675zM877.655446 481.400075l0 81.436754L877.655446 684.994006c0 22.395056-18.323321 40.718377-40.7194 40.718377l-692.235944 0c-22.396079 0-40.7194-18.323321-40.7194-40.718377L103.980701 359.237781c0-22.396079 18.323321-40.7194 40.7194-40.7194l692.235944 0c22.396079 0 40.7194 18.323321 40.7194 40.7194L877.655446 481.400075 877.655446 481.400075zM877.655446 481.400075"
                      fill="#2d4247"
                      p-id="6537"
                    />
                  </svg>
                </span>
              </div>
            </div>
            <div class="nav_s" style="background-color: #ffffff"></div>
            <!-- 内容区域 -->
            <div class="phone_conts" style="background-color: #f3f4f6">
              <Editor
                v-model="valueHtml"
                class="editor-content"
                :mode="mode"
                :default-config="editorConfig"
                @on-created="handleCreated"
                @on-blur="handleBlur"
              >
              </Editor>
            </div>
            <div class="bottom_bar" />
          </div>
          <div class="volume" />
          <div class="bang">
            <div class="yuan" />
          </div>
          <div class="power1" />
          <div class="power2" />
          <div class="power3" />
        </div>
      </div>
      <Toolbar
        style="
          height: 90%;
          margin: 10px;
          background: linear-gradient(#8c8c8c, #000000 9%, #222222);
          box-shadow: 0 6px 12px rgb(0 0 0 / 15%);
          transform: translateY(-2px);
        "
        v-if="!hideToolBar"
        :editor="editorRef"
        :default-config="toolbarConfig"
        :mode="mode"
      />
    </div>
  </div>
</template>

<script setup lang="ts" name="WangEditor">
import { nextTick, computed, inject, shallowRef, onBeforeUnmount } from "vue";
import { IToolbarConfig, IEditorConfig, DomEditor } from "@wangeditor/editor";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { realTimeInfoApi } from "@/api";
import "@wangeditor/editor/dist/css/style.css";
import { formContextKey, formItemContextKey } from "element-plus";

// 富文本 DOM 元素
const editorRef = shallowRef();
/* 手机时间 */
function showData() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const currentTime = `${hours < 10 ? "0" + hours : hours}:${minutes < 10 ? "0" + minutes : minutes}`;
  return currentTime;
}

// 实列化编辑器
const handleCreated = (editor: any) => {
  editorRef.value = editor;
};
//打印工具栏等设置
// setTimeout(() => {
//   const editor = editorRef.value
//   const toolbar = DomEditor.getToolbar(editor)
//   const curToolbarConfig = toolbar.getConfig()
//   console.log(toolbar.getConfig().toolbarKeys)
//   console.log(editor.getMenuConfig('group-image'))
// }, 2000)

// 接收父组件参数，并设置默认值
interface RichEditorProps {
  value: string; // 富文本值 ==> 必传
  toolbarConfig?: Partial<IToolbarConfig>; // 工具栏配置 ==> 非必传（默认为空）
  editorConfig?: Partial<IEditorConfig>; // 编辑器配置 ==> 非必传（默认为空）
  height?: string; // 富文本高度 ==> 非必传（默认为 500px）
  mode?: "default" | "simple"; // 富文本模式 ==> 非必传（默认为 default）
  hideToolBar?: boolean; // 是否隐藏工具栏 ==> 非必传（默认为false）
  disabled?: boolean; // 是否禁用编辑器 ==> 非必传（默认为false）
}
const props = withDefaults(defineProps<RichEditorProps>(), {
  toolbarConfig: () => {
    return {
      excludeKeys: ["group-video", "fullScreen"]
    };
  },
  editorConfig: () => {
    return {
      placeholder: "请输入内容...",
      MENU_CONF: {}
    };
  },
  height: "500px",
  mode: "default",
  hideToolBar: false,
  disabled: false
});

// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0);
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

// 判断当前富文本编辑器是否禁用
if (self_disabled.value) nextTick(() => editorRef.value.disable());

// 富文本的内容监听，触发父组件改变，实现双向数据绑定
const emit = defineEmits<{
  "update:value": [value: string];
  "check-validate": [];
}>();
const valueHtml = computed({
  get() {
    return props.value;
  },
  set(val: string) {
    // 防止富文本内容为空时，校验失败
    if (editorRef.value.isEmpty()) val = "";
    emit("update:value", val);
  }
});

/**
 * @description 图片自定义上传
 * @param file 上传的文件
 * @param insertFn 上传成功后的回调函数（插入到富文本编辑器中）
 * */
type InsertFnTypeImg = (url: string, alt?: string, href?: string) => void;
props.editorConfig.MENU_CONF!["uploadImage"] = {
  async customUpload(file: File, insertFn: InsertFnTypeImg) {
    if (!uploadImgValidate(file)) return;
    let formData = new FormData();
    formData.append("file", file);
    try {
      const res = await realTimeInfoApi.UploadRealTimeInfoImg(formData);
      if (res.code == "200") {
        insertFn("http://localhost:8000" + res.data.virtualPath);
      } else {
        ElMessage.error("上传失败!");
      }
    } catch (error) {
      console.log(error);
    }
  }
};

// 图片上传前判断
const uploadImgValidate = (file: File): boolean => {
  console.log(file);
  return true;
};

/**
 * @description 视频自定义上传
 * @param file 上传的文件
 * @param insertFn 上传成功后的回调函数（插入到富文本编辑器中）
 * */
type InsertFnTypeVideo = (url: string, poster?: string) => void;
props.editorConfig.MENU_CONF!["uploadVideo"] = {
  async customUpload(file: File, insertFn: InsertFnTypeVideo) {
    if (!uploadVideoValidate(file)) return;
    let formData = new FormData();
    formData.append("file", file);
    try {
      // const { data } = await uploadVideo(formData);
      // insertFn(data.fileUrl);
    } catch (error) {
      console.log(error);
    }
  }
};

// 视频上传前判断
const uploadVideoValidate = (file: File): boolean => {
  console.log(file);
  return true;
};

// 编辑框失去焦点时触发
const handleBlur = () => {
  formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  if (!editorRef.value) return;
  editorRef.value.destroy();
});

defineExpose({
  editor: editorRef
});
</script>

<style scoped lang="scss">
@import "./index";

/* 手机样式 */
:deep(.look_box .el-dialog__header) {
  display: none;
}
:deep(.look_box .el-dialog) {
  border-radius: 0 !important;
}
:deep(.look_box .el-dialog__body) {
  padding: 0 !important;
}

/* stylelint-disable-next-line no-duplicate-selectors */
:deep(.look_box .el-dialog) {
  background: transparent !important;
  box-shadow: none;
}

/* stylelint-disable-next-line no-duplicate-selectors */
:deep(.look_box .el-dialog__body) {
  border: none !important;
}
.html_conts img {
  width: 100%;
  height: auto;
  padding: 5px 0;
  vertical-align: bottom;
}
.look_box {
  .look_box_cont {
    height: 500px;
  }
}
.phone {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  width: 332px;
  height: 686px;
  border: 15px solid #000000;
  border-radius: 55px;
}
.phone_shadow {
  position: absolute;
  top: -9px;
  left: -9px;
  z-index: 8;
  box-sizing: border-box;
  width: 320px;
  height: 673px;
  overflow: hidden;
  content: "";
  background: #ffffff;
  border: 4px solid #000000;
  border-radius: 48px;
  box-shadow: 0 0 24px #ffffff;
  .nav {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding-right: 19px;
    padding-left: 35px;
    margin-top: 4px;
    font-size: 16px;
    color: #333333;
    .icons {
      display: flex;
      align-items: center;
    }
  }
  .nav_s {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    padding: 0 10px;
    font-size: 16px;
    background-color: #f4f4f5;
    border-bottom: 1px solid #ececec;
    .span_title {
      position: absolute;
      top: 50%;
      left: 50%;
      display: -webkit-box;
      overflow: hidden;
      font-size: 16px;
      color: #000000;
      text-overflow: ellipsis;
      transform: translate(-50%, -50%);
      -webkit-box-orient: vertical;
    }
    .left {
      font-size: 20px;
      color: #000000;
    }
    .right {
      color: #83878d;
      .one {
        margin-right: 5px;
      }
    }
  }
  .phone_conts {
    box-sizing: border-box;
    height: 550px;
    color: #000000;
    .titles {
      font-size: 16px;
      font-weight: 700;
      line-height: 30px;
    }
    .data {
      line-height: 26px;
      color: #666769;
      border-bottom: 1px dashed #c6c6c6;
    }
    .html_conts {
      margin-top: 20px;
      line-height: 30px;
      color: #666769;
      word-break: break-all;
    }
  }
  .bottom_bar {
    position: absolute;
    bottom: 5px;
    left: 50%;
    width: 120px;
    height: 4px;
    background-color: #2d4247;
    border-radius: 5px;
    transform: translateX(-50%);
  }
}
.phone_conts::-webkit-scrollbar {
  display: none;
}
.volume {
  position: absolute;
  top: 172px;
  right: -18px;
  z-index: -1;
  width: 5px;
  height: 100px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 0 20px 20px 0;
}
.power1 {
  position: absolute;
  top: 100px;
  left: -19px;
  z-index: -1;
  width: 5px;
  height: 35px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 20px 0 0 20px;
}
.power2 {
  position: absolute;
  top: 158px;
  left: -19px;
  z-index: -1;
  width: 5px;
  height: 58px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 20px 0 0 20px;
}
.power3 {
  position: absolute;
  top: 232px;
  left: -19px;
  z-index: -1;
  width: 5px;
  height: 58px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 20px 0 0 20px;
}
.receiver {
  position: absolute;
  top: 4px;
  left: 50%;
  z-index: 9;
  width: 50px;
  height: 8px;
  background: #000000;
  border: #484848 1px solid;
  border-radius: 20px;
  transform: translateX(-50%);
}
.bang {
  position: absolute;
  top: 4px;
  left: 50%;
  z-index: 9;
  width: 93px;
  height: 26px;
  background-color: #000000;
  border-radius: 13px;
  transform: translateX(-50%);
}
.yuan {
  position: absolute;
  top: 7px;
  right: 9px;
  box-sizing: border-box;
  width: 12px;
  height: 12px;
  background-color: #0f2563;
  border-radius: 50%;
}
.look_my_phone .phone_conts {
  padding: 0 !important;
  overflow: hidden !important;
}
</style>
