import { moduleRequest } from "@/api/request";
import { ResPage, SysMembers } from "@/api";
const http = moduleRequest("/admin/Members/");

const membersApi = {
  /** 用户分页 */
  page(params: SysMembers.Page) {
    return http.post<ResPage<SysMembers.Members>>("GetMembersList", params);
  },
  InputMembers(params: {}) {
    return http.post<Boolean>("InputMembers", params);
  }
};

export { membersApi };
