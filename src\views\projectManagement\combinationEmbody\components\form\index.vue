<template>
  <div>
    <form-container v-model="visible" :title="`${combinationEmbodyProps.opt}包含关系`" form-size="95%" @close="onClose">
      <div class="form-container-body">
        <div class="table-container">
          <div class="sub-title">主组合</div>
          <el-input v-model="searchCombination1" placeholder="请输入组合名称">
            <template #prepend>
              <el-select v-model="searchCombinationOption1" placeholder="" style="width: 200px" @change="onSearchCombination1">
                <el-option :label="item.label" :value="item.value" v-for="(item, index) in projectClsOptionsMap" :key="index" />
              </el-select>
            </template>
            <template #append>
              <el-button :icon="Search" @click="onSearchCombination1" />
            </template>
          </el-input>
          <el-table :data="filteredCombination1" style="width: 100%" height="100%" border>
            <el-table-column prop="combCode" label="组合编码" width="120" />
            <el-table-column prop="combName" label="组合名称" width="180" />
            <el-table-column prop="clsName" label="项目分类" width="160" />
            <el-table-column prop="price" label="价格" />
            <el-table-column fixed="right" label="操作" width="80">
              <template #default="scope">
                <el-button
                  link
                  :type="selectedMainComb?.id === scope.row.id ? 'success' : 'primary'"
                  @click="onSelectCombination(scope.row)"
                  :disabled="combinationEmbodyProps.opt === FormOptEnum.EDIT"
                  >{{ selectedMainComb?.id === scope.row.id ? "已选择" : "选择" }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-container">
          <div class="sub-title">包含组合</div>
          <el-input v-model="searchCombination2" placeholder="请输入组合名称">
            <template #prepend>
              <el-select v-model="searchCombinationOption2" placeholder="" style="width: 200px" @change="onSearchCombination2">
                <el-option :label="item.label" :value="item.value" v-for="(item, index) in projectClsOptionsMap" :key="index" />
              </el-select>
            </template>
            <template #append>
              <el-button :icon="Search" @click="onSearchCombination2" />
            </template>
          </el-input>
          <el-table :data="filteredCombination2" style="width: 100%" height="100%" border>
            <el-table-column prop="clsName" label="项目分类" width="160" />
            <el-table-column prop="combCode" label="组合编码" width="120" />
            <el-table-column prop="combName" label="组合名称" width="180" />
            <el-table-column prop="price" label="价格" />
            <el-table-column fixed="right" label="操作" width="80">
              <template #default="scope">
                <el-button link type="primary" @click="onAddCombination(scope.row)">添加</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-container">
          <div class="sub-title">已选择主组合：{{ selectedMainComb?.combName }}</div>
          <div class="selected-header">
            <span class="selected-title">已选择组合</span>
            <span class="selected-count">共 {{ selectedCombination.length }} 项</span>
            <span class="selected-price">总价: {{ totalPrice.toFixed(2) }}元</span>
          </div>
          <el-table :data="selectedCombination" style="width: 100%" height="100%" border>
            <!-- <el-table-column prop="clsName" label="项目分类" width="160" /> -->
            <el-table-column prop="combCode" label="组合编码" width="120" />
            <el-table-column prop="combName" label="组合名称" width="180" />
            <el-table-column prop="price" label="价格" />
            <el-table-column fixed="right" label="操作" width="80">
              <template #default="scope">
                <el-button link type="danger" @click="onRemoveCombination(scope.row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!combinationEmbodyProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { CombinationEmbody, combinationEmbodyApi, combinationManagementApi, CombinationManagement } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { useDictStore } from "@/stores/modules";
import { Search } from "@element-plus/icons-vue";

const visible = ref(false); //是否显示表单

// 表单参数
const combinationEmbodyProps = reactive<FormProps.Base<CombinationEmbody.CombinationEmbodyInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

const dictStore = useDictStore(); //字典仓库
// 项目分类选项
const projectClsOptions = dictStore.getDictList(SysDictEnum.PROJECT_CLS);
// 筛选后的组合数据
const filteredCombination1 = ref<CombinationManagement.CombinationInfo[]>([]);
// 筛选后的组合数据
const filteredCombination2 = ref<CombinationManagement.CombinationInfo[]>([]);
// 组合数据
const combinationData = ref<CombinationManagement.CombinationInfo[]>([]);
// 已选择组合数据
const selectedCombination = ref<CombinationManagement.CombinationInfo[]>([]);
// 搜索组合名称
const searchCombination1 = ref("");
const searchCombination2 = ref("");
// 搜索项目分类选项
const searchCombinationOption1 = ref("-1");
const searchCombinationOption2 = ref("-1");
const combinationOptions = ref<{ label: string; value: string }[]>([]);
const selectedMainComb = ref<CombinationManagement.CombinationInfo | null>(null);
// 计算总价
const totalPrice = computed(() => {
  const total = selectedCombination.value.reduce((sum, item) => sum + item.price, 0);
  return Number(total.toFixed(2));
});
/** 计算项目分类选项 */
const projectClsOptionsMap = computed(() => {
  return [{ value: "-1", label: "全部项目分类" }, ...projectClsOptions];
});
/**
 * 打开表单
 * @param props 表单参数
 */
async function onOpen(props: FormProps.Base<CombinationEmbody.CombinationEmbodyInfo>) {
  Object.assign(combinationEmbodyProps, props); //合并参数
  await getCombsByItemCls();
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    selectedMainComb.value = null; //清空主组合
    selectedCombination.value = []; //清空已选择组合
  }
  visible.value = true; //显示表单
  if (props.opt === FormOptEnum.EDIT) {
    combinationEmbodyProps.record = JSON.parse(JSON.stringify(props.record));
    selectedMainComb.value = combinationData.value.find(item => item.combCode === props.record.combCode!) || null;
    //获取依赖组合
    combinationEmbodyApi.detail({ combCode: props.record.combCode! }).then(res => {
      const combCode = res.data.flatMap(item => item.childCombCode);
      selectedCombination.value = combinationData.value.filter(item => combCode.includes(item.combCode));
    });
  }
}

/** 提交表单 */
async function handleSubmit() {
  if (!selectedMainComb.value) {
    ElMessage.warning("请选择主组合");
    return;
  }
  const params = selectedCombination.value.map(item => ({
    combCode: selectedMainComb.value.combCode,
    combName: selectedMainComb.value.combName,
    childCombCode: item.combCode,
    childCombName: item.combName
  }));
  //提交表单
  await combinationEmbodyApi
    .submitForm(params)
    .then(() => {
      ElMessage.success("保存成功");
      combinationEmbodyProps.successful!(); //调用父组件的successful方法
    })
    .finally(() => {
      onClose();
    });
}

/** 搜索组合 */
function onSearchCombination1() {
  // 先根据分类过滤基础数据
  const baseData =
    searchCombinationOption1.value === "-1"
      ? combinationData.value
      : combinationData.value.filter(item => item.clsCode === searchCombinationOption1.value);

  // 再根据搜索词过滤组合名称
  filteredCombination1.value = baseData.filter(comb => comb.combName.includes(searchCombination1.value));
}
/** 搜索组合 */
function onSearchCombination2() {
  // 先根据分类过滤基础数据
  const baseData =
    searchCombinationOption2.value === "-1"
      ? combinationData.value
      : combinationData.value.filter(item => item.clsCode === searchCombinationOption2.value);

  // 再根据搜索词过滤组合名称
  filteredCombination2.value = baseData.filter(comb => comb.combName.includes(searchCombination2.value));
}
/** 添加组合 */
function onAddCombination(row: CombinationManagement.CombinationInfo) {
  if (selectedMainComb.value == null) {
    ElMessage.warning("请先选择主组合");
    return;
  }
  if (selectedMainComb.value.combCode === row.combCode) {
    ElMessage.warning("同一组合不能包含");
    return;
  }

  if (selectedCombination.value.some(item => item.combCode === row.combCode)) {
    ElMessage.warning("已存在该组合");
    return;
  }
  const addPackageCode = filteredCombination2.value.some(item => item.combCode === row.combCode);
  if (addPackageCode) {
    selectedCombination.value.push(row);
  }
}
/**
 * 移除组合
 * @param row 组合数据
 */
function onRemoveCombination(row: CombinationManagement.CombinationInfo) {
  const index = selectedCombination.value.findIndex(item => item.combCode === row.combCode);
  if (index !== -1) {
    selectedCombination.value.splice(index, 1);
  }
}
/** 选择主组合 */
function onSelectCombination(row: CombinationManagement.CombinationInfo) {
  selectedMainComb.value = row;
  selectedCombination.value = [];
}
/** 获取项目组合 */
async function getCombsByItemCls() {
  const { data } = await combinationManagementApi.getCombination();
  filteredCombination1.value = data;
  filteredCombination2.value = data;
  combinationData.value = data;
  combinationOptions.value = data.map(item => ({ label: item.combName, value: item.combCode }));
}
/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container-body {
  display: grid;
  grid-template-rows: 70vh;
  grid-template-columns: 400px 1fr 1fr;
  gap: 15px;
  margin-top: 20px;
  .price-unit {
    margin-left: 10px;
  }
  .age-limit {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .table-container {
    display: grid;
    grid-template-rows: 20px auto 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
    .sub-title {
      font-size: 16px;
      font-weight: bold;
    }
    .selected-header {
      display: grid;
      grid-template-columns: 84px 1fr 1fr;
      align-items: center;
      padding: 6px 8px;
      font-weight: bold;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
    .combination-container {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px;
      overflow: auto;
      border-radius: 4px;
      outline: 1px solid var(--el-border-color);
      .combination-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .combination-title {
        font-weight: bold;
      }
      .combination-checkbox-group {
        margin-left: 20px;
      }
      .combination-checkbox {
        margin-bottom: 20px;
      }
    }
    .selected-count {
      font-weight: normal;
    }
    .selected-price {
      color: var(--el-color-danger);
      text-align: right;
    }
  }
}
:deep(.el-dialog__body) {
  margin-top: 0;
}
:deep(.el-drawer__body) {
  margin-top: -20px;
}
</style>
