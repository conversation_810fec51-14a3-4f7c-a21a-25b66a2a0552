<template>
  <div class="table-container">
    <div class="table-item">
      <el-input v-model="searchCombination" placeholder="请输入组合名称">
        <template #prepend>
          <el-select v-model="searchCombinationOption" placeholder="" style="width: 200px" @change="onSearchCombination">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in projectClsOptionsMap" :key="index" />
          </el-select>
        </template>
        <template #append>
          <el-button :icon="Search" @click="onSearchCombination" />
        </template>
      </el-input>
      <div class="combination-container">
        <div v-for="(item, index) in filteredCombination" :key="index" class="combination-item">
          <div class="combination-title">{{ item.clsName }}</div>
          <el-checkbox-group v-model="selectedCombination" class="combination-checkbox-group">
            <el-checkbox
              :label="comb.combCode"
              border
              :disabled="disabledItems.includes(comb.combCode)"
              class="combination-checkbox"
              v-for="(comb, combIndex) in item.combData"
              :key="combIndex"
              >{{ `${comb.combName}　　￥${comb.price}` }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
        <el-empty v-if="filteredCombination.length === 0" description="暂无数据" />
      </div>
    </div>
    <div class="table-item">
      <div class="selected-header">
        <span class="selected-title">已选择组合</span>
        <span class="selected-count">共 {{ selectedCombination.length }} 项</span>
        <span class="selected-price">总价：{{ totalPrice.toFixed(2) }}元</span>
      </div>
      <el-table :data="selectedCombinationData" style="width: 100%" height="100%" border>
        <el-table-column prop="combCode" label="组合编码" />
        <el-table-column prop="combName" label="组合名称" />
        <el-table-column prop="price" label="价格" />
        <el-table-column fixed="right" label="操作" width="110">
          <template #default="scope">
            <el-button link type="danger" @click="onRemoveCombination(scope.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { IndividualPackage, individualPackageApi } from "@/api";
import { Search } from "@element-plus/icons-vue";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { useDictStore } from "@/stores/modules";
const dictStore = useDictStore(); //字典仓库
// 项目分类选项
const projectClsOptions = dictStore.getDictList(SysDictEnum.PROJECT_CLS);
// 筛选后的组合数据
const filteredCombination = ref<IndividualPackage.CombsByItemCls[]>([]);
// 组合数据
const combinationData = ref<IndividualPackage.CombsByItemCls[]>([]);
// 搜索组合名称
const searchCombination = ref("");
// 搜索项目分类选项
const searchCombinationOption = ref("-1");
// 已选择组合数据
const selectedCombination = ref<string[]>([]);
// 禁用项
const disabledItems = ref<string[]>([]);
// 表单参数
const individualPackageProps = reactive<FormProps.Base<IndividualPackage.IndividualPackageInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 计算总价
const totalPrice = computed(() => {
  const codes = selectedCombination.value;
  const allCombs = combinationData.value.flatMap(item => item.combData);
  const total = allCombs.filter(comb => codes.includes(comb.combCode)).reduce((sum, item) => sum + item.price, 0);
  individualPackageProps.record.price = Number(total.toFixed(2));
  return Number(total.toFixed(2));
});
/** 计算已选择组合的数据 */
const selectedCombinationData = computed(() => {
  const codes = selectedCombination.value;
  const allCombs = combinationData.value.flatMap(item => item.combData);
  return allCombs.filter(comb => codes.includes(comb.combCode));
});
/** 计算项目分类选项 */
const projectClsOptionsMap = computed(() => {
  return [{ value: "-1", label: "全部项目分类" }, ...projectClsOptions];
});
/** 搜索组合 */
function onSearchCombination() {
  // 先根据分类过滤基础数据
  const baseData =
    searchCombinationOption.value === "-1"
      ? combinationData.value
      : combinationData.value.filter(item => item.clsCode === searchCombinationOption.value);

  // 再根据搜索词过滤组合名称
  filteredCombination.value = baseData
    .map(item => ({
      ...item,
      combData: item.combData.filter(comb => comb.combName.includes(searchCombination.value))
    }))
    .filter(item => item.combData.length > 0);
}
/**
 * 移除组合
 * @param row 组合数据
 */
function onRemoveCombination(row: IndividualPackage.CombinationData) {
  selectedCombination.value = selectedCombination.value.filter(item => item !== row.combCode);
}
// 清除搜索条件
function onClear() {
  searchCombination.value = "";
  searchCombinationOption.value = "-1";
  onSearchCombination();
}

// 重置选中方法
function resetSelection() {
  selectedCombination.value = [];
}
// 设置禁用项
function setDisabledItems(codes: string[]) {
  disabledItems.value = codes;
}
/** 获取项目组合 */
async function getCombsByItemCls() {
  const { data } = await individualPackageApi.getCombsByItemCls();
  filteredCombination.value = data;
  combinationData.value = data;
}
defineExpose({
  filteredCombination,
  combinationData,
  selectedCombinationData,
  selectedCombination,
  onClear,
  setDisabledItems,
  resetSelection,
  getCombsByItemCls
});
</script>

<style lang="scss" scoped>
.table-container {
  display: grid;
  grid-template-rows: 46vh;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  height: 100%;
  .table-item {
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
    .selected-header {
      display: grid;
      grid-template-columns: 86px 1fr 1fr;
      gap: 10px;
      align-items: center;
      padding: 6px 8px;
      font-size: 14px;
      font-weight: bold;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
    .combination-container {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px;
      overflow: auto;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      .combination-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .combination-title {
        font-size: 14px;
        font-weight: bold;
      }
      .combination-checkbox-group {
        margin-left: 20px;
      }
      .combination-checkbox {
        margin-bottom: 20px;
      }
    }
    .selected-count {
      font-weight: normal;
    }
    .selected-price {
      color: var(--el-color-danger);
      text-align: right;
    }
  }
}
</style>
