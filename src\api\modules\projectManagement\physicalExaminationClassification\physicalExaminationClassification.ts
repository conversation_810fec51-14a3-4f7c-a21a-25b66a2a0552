/**
 * @description 体检分类接口
 */
import { PhysicalExaminationClassification } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/admin/basiccode/");

const physicalExaminationClassificationApi = {
  /** 获取体检分类 */
  page() {
    return http.post<PhysicalExaminationClassification.PhysicalExaminationClassificationInfo[]>("getcodepecls");
  },
  /** 新增/修改体检分类 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "editcodepecls" : "addcodepecls", params);
  },
  /** 删除体检分类 */
  delete(params: {}) {
    return http.post("deletecodepecls", params);
  }
};

export { physicalExaminationClassificationApi };
