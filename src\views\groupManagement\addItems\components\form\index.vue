<template>
  <div class="form-container card" v-show="visible">
    <div>
      <div class="form-title">
        {{ individualPackageProps.opt === FormOptEnum.ADD ? `新增套餐` : `编辑套餐：${individualPackageProps.record.clusName}` }}
      </div>
      <el-divider border-style="double" class="form-divider" />
    </div>
    <el-form
      ref="individualPackageFormRef"
      :rules="rules"
      :model="individualPackageProps.record"
      label-width="auto"
      label-suffix=" :"
      class="form-box"
    >
      <s-form-item label="套餐编码" prop="clusCode">
        <s-input v-model="individualPackageProps.record.clusCode" placeholder="请输入套餐编码" disabled></s-input>
      </s-form-item>
      <s-form-item label="套餐名称" prop="clusName">
        <s-input v-model="individualPackageProps.record.clusName" placeholder="请输入套餐名称"></s-input>
      </s-form-item>
      <s-form-item label="套餐价格" prop="price">
        <div class="input-number-wrapper">
          <el-input-number v-model="individualPackageProps.record.price" :precision="2" :min="0" :controls="false" disabled />
          <span>元</span>
        </div>
      </s-form-item>
      <s-form-item label="适用性别" prop="gender">
        <s-radio-group v-model="individualPackageProps.record.gender" :options="genderOptions"></s-radio-group>
      </s-form-item>
      <s-form-item label="启用状态" prop="isEnabled">
        <s-radio-group v-model="individualPackageProps.record.isEnabled" :options="statusOptions"></s-radio-group>
      </s-form-item>
      <s-form-item label="体检分类" prop="peCls">
        <s-select v-model="individualPackageProps.record.peCls" :options="peClsOptions"></s-select>
      </s-form-item>
      <s-form-item label="年龄限制" prop="isAgeLimit">
        <s-radio-group v-model="individualPackageProps.record.isAgeLimit" :options="statusOptions"></s-radio-group>
      </s-form-item>
      <s-form-item label="年龄区间" prop="ageLimit" v-if="individualPackageProps.record.isAgeLimit === 'Y'">
        <div class="age-limit">
          <el-input-number v-model="individualPackageProps.record.lowerAgeLimit" :precision="0" :min="0" :controls="false" placeholder="最小年龄" />
          至
          <el-input-number v-model="individualPackageProps.record.upperAgeLimit" :precision="0" :min="0" :controls="false" placeholder="最大年龄" />
        </div>
      </s-form-item>
      <s-form-item label="加项规则" prop="addRule">
        <s-select v-model="individualPackageProps.record.addRule" :options="addProjectOptions"></s-select>
      </s-form-item>
      <s-form-item label="套餐简介" prop="description">
        <s-input
          v-model="individualPackageProps.record.description"
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          placeholder="请输入套餐简介"
        ></s-input>
      </s-form-item>
      <s-form-item label="注意事项" prop="notice">
        <s-input
          v-model="individualPackageProps.record.notice"
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          placeholder="请输入注意事项"
        ></s-input>
      </s-form-item>
    </el-form>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="添加项目" :name="TabNameEnum.添加项目">
        <ProjectTable ref="projectTableRef" />
      </el-tab-pane>
      <el-tab-pane label="额外加项" :name="TabNameEnum.额外加项" v-if="individualPackageProps.opt === FormOptEnum.EDIT">
        <ProjectTable ref="extraProjectTableRef" />
      </el-tab-pane>
      <el-tab-pane label="加项包" :name="TabNameEnum.加项包" v-if="individualPackageProps.opt === FormOptEnum.EDIT">
        <AddPackageTable ref="addPackageTableRef" />
      </el-tab-pane>
    </el-tabs>
    <div class="operation">
      <el-button @click="onClose"> 取消 </el-button>
      <el-button type="primary" @click="handleSubmit"> 保存 </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IndividualPackage, individualPackageApi, addPackageApi, AddPackage } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";
import type { TabsPaneContext } from "element-plus";
import ProjectTable from "./projectTable.vue";
import AddPackageTable from "./addPackageTable.vue";

/**
 * 标签页参数
 */
enum TabNameEnum {
  "添加项目" = 1,
  "额外加项" = 2,
  "加项包" = 3
}
const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
// 性别选项
const genderOptions = dictStore.getDictList(SysDictEnum.GENDER);
// 加项规则选项
const addProjectOptions = dictStore.getDictList(SysDictEnum.ADD_PROJECT_RULE);
// 体检分类选项
const peClsOptions = dictStore.getDictList(SysDictEnum.PE_CLS);
// 表单参数
const individualPackageProps = reactive<FormProps.Base<IndividualPackage.IndividualPackageInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});
// 当前标签页
const activeName = ref(TabNameEnum.添加项目);
// 添加项目表格
const projectTableRef = ref<InstanceType<typeof ProjectTable> | null>(null);
// 额外加项表格
const extraProjectTableRef = ref<InstanceType<typeof ProjectTable> | null>(null);
// 加项包表格
const addPackageTableRef = ref<InstanceType<typeof AddPackageTable> | null>(null);

// 表单验证规则
const rules = reactive({
  clusName: [required("请输入套餐名称")],
  gender: [required("请选择性别")],
  isEnabled: [required("请选择启用状态")],
  peCls: [required("请选择体检分类")],
  isAgeLimit: [required("请选择年龄限制")],
  addRule: [required("请选择加项规则")]
});

// 添加计算属性获取子组件总价
const totalPrice = computed(() => {
  const projectPrice = projectTableRef.value?.selectedCombinationData?.reduce((sum, item) => sum + (item.price || 0), 0) || 0;
  return Number(projectPrice.toFixed(2));
});
/**
 * 打开表单
 * @param props 表单参数
 */
async function onOpen(props: FormProps.Base<IndividualPackage.IndividualPackageInfo>) {
  Object.assign(individualPackageProps, props); //合并参数
  activeName.value = TabNameEnum.添加项目;
  await projectTableRef.value?.getCombsByItemCls();

  if (props.opt == FormOptEnum.ADD) {
    individualPackageFormRef.value?.resetFields();
    projectTableRef.value?.resetSelection();
    extraProjectTableRef.value?.resetSelection();

    //如果是新增,设置默认值
    individualPackageProps.record = {
      id: "",
      clusCode: "",
      clusName: "",
      price: 0,
      gender: genderOptions[0].value,
      isEnabled: statusOptions[0].value,
      peCls: peClsOptions[0].value,
      addRule: addProjectOptions[2].value,
      isAgeLimit: statusOptions[0].value,
      lowerAgeLimit: 0,
      upperAgeLimit: 0,
      description: "",
      notice: ""
    };
  }
  visible.value = true; //显示表单
  if (props.opt == FormOptEnum.EDIT) {
    await extraProjectTableRef.value.getCombsByItemCls();
    await addPackageTableRef.value.getAddPackageList();
    // 添加项目
    individualPackageApi.getClusterByComb({ clusCode: props.record.clusCode }).then(res => {
      projectTableRef.value!.selectedCombination = res.data.map(item => item.combCode);
      projectTableRef.value!.selectedCombinationData = res.data;
    });
    // 额外加项
    individualPackageApi.getClusterExtraComb({ clusCode: props.record.clusCode }).then(res => {
      extraProjectTableRef.value!.selectedCombination = res.data.map(item => item.combCode);
      extraProjectTableRef.value!.selectedCombinationData = res.data;
    });
    individualPackageApi.getClusterAddPackage({ clusCode: props.record.clusCode }).then(res => {
      addPackageTableRef.value!.selectedAddPackageData = res.data;
    });
  }
}

// 提交数据（新增/编辑）
const individualPackageFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  individualPackageFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    await submit();
  });
}
/**
 * 提交数据
 */
async function submit() {
  // 定义提交处理器
  const submitData = async (apiMethod: (params: any) => Promise<any>, params: any[], successCallback?: () => void) => {
    await apiMethod(params)
      .then(() => individualPackageProps.successful!())
      .finally(onClose);
  };

  // 根据当前标签页选择提交方式
  const handlers = {
    [TabNameEnum.添加项目]: {
      data: projectTableRef.value?.selectedCombinationData,
      api: individualPackageApi.submitForm,
      paramsBuilder: (data: any) => ({
        ...individualPackageProps.record,
        clusterCombs: data.map((item: any) => ({
          clusCode: individualPackageProps.record.clusCode,
          combCode: item.combCode,
          price: item.price
        }))
      })
    },
    [TabNameEnum.额外加项]: {
      data: extraProjectTableRef.value?.selectedCombinationData,
      api: individualPackageApi.saveClusterExtraComb,
      paramsBuilder: (data: any) =>
        data.map((item: any) => ({
          clusCode: individualPackageProps.record.clusCode,
          combCode: item.combCode,
          price: item.price
        }))
    },
    [TabNameEnum.加项包]: {
      data: addPackageTableRef.value?.selectedAddPackageData,
      api: individualPackageApi.saveClusterAddPackage,
      paramsBuilder: (data: any) =>
        data.map((item: any) => ({
          clusCode: individualPackageProps.record.clusCode,
          addPackageCode: item.addPackageCode,
          price: item.price
        }))
    }
  };

  const currentHandler = handlers[activeName.value];
  if ((currentHandler?.data?.length ?? 0) > 0) {
    const params = currentHandler.paramsBuilder(currentHandler.data!);
    await submitData(currentHandler.api, params);
  }
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}
// 标签页点击
function handleClick(tab: TabsPaneContext) {
  const paneName = tab.paneName;
  projectTableRef.value?.onClear();
  extraProjectTableRef.value?.onClear();
  if (paneName === TabNameEnum.添加项目) {
    // 当切换到添加项目标签页时，过滤额外加项的可选内容
    const selectedCodes = extraProjectTableRef.value?.selectedCombination || [];
    projectTableRef.value?.setDisabledItems(selectedCodes);
  } else if (paneName === TabNameEnum.额外加项) {
    // 当切换到额外加项标签页时，过滤添加项目的可选内容
    const selectedCodes = projectTableRef.value?.selectedCombination || [];
    extraProjectTableRef.value?.setDisabledItems(selectedCodes);
  }
}

// 监听总价变化自动更新表单值
watch(totalPrice, newVal => {
  individualPackageProps.record.price = newVal;
});

// 暴露给父组件的方法
defineExpose({
  visible,
  onOpen,
  onClose
});
</script>

<style lang="scss" scoped>
.form-container {
  display: grid;
  grid-template-rows: 50px auto 1fr 52px;
  .form-title {
    font-size: 18px;
    font-weight: bold;
  }
  .form-divider {
    margin: 12px 0;
  }
  .form-box {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    align-items: center;
  }
  .input-number-wrapper {
    display: flex;
    gap: 10px;
  }
  .age-limit {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .operation {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
:deep(.el-form-item) {
  margin-right: 12px;
}
:deep(.el-input-number) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding: 0;
  .el-input__inner {
    font-size: 1.1em;
    text-align: right;
  }
}
</style>
