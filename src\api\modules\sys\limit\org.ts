/**
 * @description 组织机构管理
 */

import { ReqId, ResPage, SysOrg } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/org/");

const sysOrgApi = {
  /** 获取分页 */
  page(params: SysOrg.Page) {
    return http.get<ResPage<SysOrg.SysOrgInfo>>("page", params);
  },

  /** 获取详情 */
  detail(params: ReqId) {
    return http.get<SysOrg.SysOrgInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除机构 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 更新头像 */
  updateAvatar(params: any) {
    return http.post<string>("updateAvatar", params, { loading: false });
  }
};

export { sysOrgApi };
