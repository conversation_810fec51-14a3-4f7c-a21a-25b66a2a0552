/*
 * @Author: Reaper
 * @Date: 2025-05-15 16:25:40
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-05 18:22:21
 * @Description: 请填写简介
 */
/**
 * Excel工具函数
 */

import { utils, read } from "xlsx";
import * as ExcelJS from "exceljs";
interface Column {
  header: string;
  key: string;
  width?: number;
  style?: any;
}

interface Row {
  [key: string]: any;
}

/**
 * xlsx 转 js 对象
 * @param {File} file 可以从 <input type="file"> 的 change 事件中获取到，可以参考项目使用的 UI 库的文档，这里省略具体步骤
 * @returns {Promise<Array<Object>>}
 */

export class ExcelImporter {
  /**
   * 创建自定义的Excel文件
   * @param {headers,rows,fileName}  上传的自定义对象
   * @returns Promise<Array> 解析后的数据数组
   */
  static async createExcelFile(headers: Column[], rows: Row[], fileName: string): Promise<Blob> {
    // 创建一个新的工作簿
    const workbook = new ExcelJS.Workbook();

    // 创建一个工作表
    const worksheet = workbook.addWorksheet(fileName);
    // 设置列
    worksheet.columns = headers.map(header => ({
      header: header.header,
      key: header.key,
      width: header.width || 15,
      style: header.style || {}
    }));

    // 添加行数据
    rows.forEach(row => {
      worksheet.addRow(row);
    });

    // 返回 Promise<Blob>
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

    // 可选：自动下载
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `${fileName}.xlsx`;
    link.click();

    console.log("Excel file created successfully!");
    return blob;
  }

  /**
   * 下载静态模板文件
   * @param fileName 文件名
   */
  static async downloadStaticTemplate(fileName: string) {
    const link = document.createElement("a");
    link.href = `/static/templates/${fileName}`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  static async importXlsx(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = event => {
        try {
          const data = event.target?.result;
          // 读取工作簿
          const workbook = read(data, { type: "binary" });
          // 读取第一个工作表，如需读取指定工作表，请改为 workbook.Sheets['工作表名称']
          const sheet = workbook.Sheets[workbook.SheetNames[0]];
          // 将工作表转为 js 对象
          const json = utils.sheet_to_json(sheet);
          console.log("json", json);
          resolve(json);
        } catch (e) {
          reject(e);
        }
      };
      reader.onerror = reject;
      reader.readAsBinaryString(file);
    });
  }
}
