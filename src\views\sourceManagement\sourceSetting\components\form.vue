<!-- 号源设置表单页面-->
<template>
  <form-container v-model="visible" :title="`${sourceProps.opt}号源`" form-size="600px">
    <el-form
      ref="sourceFormRef"
      :rules="rules"
      :disabled="sourceProps.disabled"
      :model="sourceProps.record"
      :hide-required-asterisk="sourceProps.disabled"
      label-width="auto"
      label-suffix=" :"
      class="-mt-20px"
    >
      <s-form-item label="号源名称" prop="name">
        <s-input v-model="sourceProps.record.name"></s-input>
      </s-form-item>
      <s-form-item label="号源日期" prop="sourceDate">
        <el-date-picker v-model="sourceProps.record.sourceDate" type="date" placeholder="选择日期" style="width: 100%" />
      </s-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <s-form-item label="开始时间" prop="startTime">
            <el-time-picker v-model="sourceProps.record.startTime" placeholder="选择时间" style="width: 100%" />
          </s-form-item>
        </el-col>
        <el-col :span="12">
          <s-form-item label="结束时间" prop="endTime">
            <el-time-picker v-model="sourceProps.record.endTime" placeholder="选择时间" style="width: 100%" />
          </s-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <s-form-item label="号源容量" prop="capacity">
            <el-input-number v-model="sourceProps.record.capacity" :min="1" style="width: 100%" />
          </s-form-item>
        </el-col>
        <el-col :span="12">
          <s-form-item label="已预约数量" prop="reserved">
            <el-input-number v-model="sourceProps.record.reserved" :min="0" style="width: 100%" />
          </s-form-item>
        </el-col>
      </el-row>
      <s-form-item label="号源状态" prop="status">
        <s-radio-group v-model="sourceProps.record.status" :options="statusOptions" button />
      </s-form-item>
      <s-form-item label="排序" prop="sortCode">
        <el-slider v-model="sourceProps.record.sortCode" show-input :min="1" />
      </s-form-item>
      <s-form-item label="备注" prop="remark">
        <el-input v-model="sourceProps.record.remark" type="textarea" :rows="3" />
      </s-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose"> 取消 </el-button>
      <el-button v-show="!sourceProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
    </template>
  </form-container>
</template>

<script setup lang="ts">
import { numberSourceApi, numberSource } from "@/api";
import { required } from "@/utils/formRules";
import { useDictStore } from "@/stores/modules";
import { FormOptEnum, CommonStatusEnum, SysDictEnum } from "@/enums";
import { FormInstance } from "element-plus";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库

// 通用状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);

// 表单参数
const sourceProps = reactive<FormProps.Base<numberSource.SourceInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单校验规则
const rules = {
  name: [required("请输入号源名称")],
  sourceDate: [required("请选择号源日期")],
  startTime: [required("请选择开始时间")],
  endTime: [required("请选择结束时间")],
  capacity: [required("请输入号源容量")],
  status: [required("请选择号源状态")],
  sortCode: [required("请设置排序")]
};

// 表单实例
const sourceFormRef = ref<FormInstance>();

// 打开弹窗
const openDialog = (opt: FormOptEnum, row?: numberSource.SourceInfo) => {
  visible.value = true;
  sourceProps.opt = opt;
  sourceProps.disabled = opt === FormOptEnum.VIEW;
  // 编辑/查看
  if (opt !== FormOptEnum.ADD) {
    sourceProps.record = { ...row };
  } else {
    // 新增
    sourceProps.record = {
      status: CommonStatusEnum.ENABLE,
      sortCode: 100,
      capacity: 10,
      reserved: 0
    } as numberSource.SourceInfo;
  }
};

// 关闭弹窗
const onClose = () => {
  visible.value = false;
  sourceFormRef.value?.resetFields();
  sourceProps.record = {};
};

// 提交表单
const handleSubmit = async () => {
  await sourceFormRef.value?.validate(async valid => {
    if (valid) {
      await numberSourceApi.submitForm(sourceProps.record, sourceProps.opt !== FormOptEnum.ADD);
      onClose();
      ElMessage.success(`${sourceProps.opt}成功`);
      emit("success");
    }
  });
};

// 定义事件
const emit = defineEmits(["success"]);

// 暴露方法
defineExpose({
  openDialog
});
</script>
