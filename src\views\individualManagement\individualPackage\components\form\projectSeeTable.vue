<template>
  <div class="table-container">
    <div class="table-item">
      <div class="selected-header">
        <span class="selected-title">已选择组合</span>
        <span class="selected-count">共 {{ selectedCombination.length }} 项</span>
        <span class="selected-price">总价：{{ totalPrice.toFixed(2) }}元</span>
      </div>
      <el-table :data="selectedCombination" style="width: 100%" height="100%" border>
        <el-table-column prop="combCode" label="组合编码" />
        <el-table-column prop="combName" label="组合名称" />
        <el-table-column prop="price" label="价格" />
      </el-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { IndividualPackage } from "@/api";
// 已选择组合数据
const selectedCombination = ref<IndividualPackage.CombinationData[]>([]);

// 计算总价
const totalPrice = computed(() => {
  const total = selectedCombination.value.reduce((sum, item) => sum + item.price, 0);
  return Number(total.toFixed(2));
});

defineExpose({
  selectedCombination
});
</script>

<style lang="scss" scoped>
.table-container {
  display: grid;
  grid-template-rows: 40vh;
  grid-template-columns: 1fr;
  gap: 15px;
  height: 100%;
  .table-item {
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
    .selected-header {
      display: grid;
      grid-template-columns: 86px 1fr 1fr;
      gap: 10px;
      align-items: center;
      padding: 6px 8px;
      font-size: 14px;
      font-weight: bold;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
    .combination-container {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px;
      overflow: auto;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      .combination-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .combination-title {
        font-size: 14px;
        font-weight: bold;
      }
      .combination-checkbox-group {
        margin-left: 20px;
      }
      .combination-checkbox {
        margin-bottom: 20px;
      }
    }
    .selected-count {
      font-weight: normal;
    }
    .selected-price {
      color: var(--el-color-danger);
      text-align: right;
    }
  }
}
</style>
