<template>
  <div class="package-item-container">
    <el-row :gutter="20">
      <!-- 左侧：搜索体检套餐 -->
      <el-col :span="8">
        <el-card class="search-card">
          <template #header>
            <div class="card-header">
              <span class="title">体检套餐列表</span>
            </div>
          </template>

          <div class="search-form">
            <el-form :model="searchForm" class="demo-form-inline">
              <el-form-item label="套餐名称">
                <el-input v-model="searchForm.name" placeholder="请输入套餐名称" clearable style="width: 100%" />
              </el-form-item>
              <el-form-item label="套餐类型">
                <el-select v-model="searchForm.type" placeholder="请选择套餐类型" clearable style="width: 100%">
                  <el-option v-for="item in packageTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchPackages">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 套餐列表 -->
          <div class="package-list">
            <el-table
              :data="packageList"
              style="width: 100%"
              border
              stripe
              highlight-current-row
              @row-click="handlePackageSelect"
              :header-cell-style="{
                background: '#f5f7fa',
                color: '#303133',
                fontWeight: 'bold',
                fontSize: '14px'
              }"
              :cell-style="{
                fontSize: '14px'
              }"
              height="calc(100vh - 250px)"
            >
              <el-table-column type="index" width="50" align="center" />
              <el-table-column prop="name" label="套餐名称" min-width="130" show-overflow-tooltip />
              <el-table-column label="套餐类型" width="100" show-overflow-tooltip>
                <template #default="scope">
                  {{ getPackageTypeLabel(scope.row.type) }}
                </template>
              </el-table-column>
              <el-table-column label="适用性别" width="80" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.gender === 1 ? 'primary' : scope.row.gender === 2 ? 'success' : 'info'" effect="plain">
                    {{ getGenderText(scope.row.gender) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="70" align="center" fixed="right">
                <template #default="scope">
                  <el-button type="primary" @click.stop="selectPackage(scope.row)">选择</el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[5, 10, 20]"
                layout="total, sizes, prev, pager, next"
                :total="total"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：选中的套餐和添加项目 -->
      <el-col :span="16">
        <el-card class="selected-package-card" v-if="selectedPackage.id">
          <template #header>
            <div class="card-header">
              <span class="title">已选择套餐: {{ selectedPackage.name }}</span>
              <el-button type="danger" size="small" @click="clearSelectedPackage">取消选择</el-button>
            </div>
          </template>

          <div class="package-info">
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="套餐名称">{{ selectedPackage.name }}</el-descriptions-item>
              <el-descriptions-item label="套餐类型">{{ getPackageTypeLabel(selectedPackage.type) }}</el-descriptions-item>
              <el-descriptions-item label="适用性别">{{ getGenderText(selectedPackage.gender) }}</el-descriptions-item>
              <el-descriptions-item label="年龄区间">
                {{ selectedPackage.minAge }} - {{ selectedPackage.maxAge === 0 ? "不限" : selectedPackage.maxAge }}
              </el-descriptions-item>
              <el-descriptions-item label="套餐价格">
                <span class="price-value">{{ selectedPackage.price.toFixed(2) }}</span> 元
              </el-descriptions-item>
              <el-descriptions-item label="已有项目数">{{ selectedPackage.examItems?.length || 0 }} 项</el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 添加体检项目和加项 -->
          <div class="add-items-section">
            <el-divider content-position="left">添加体检项目和加项</el-divider>

            <el-tabs v-model="activeTab" class="item-tabs">
              <el-tab-pane label="体检项目" name="examItems">
                <div class="search-box">
                  <el-input v-model="searchKeyword" placeholder="搜索体检项目" clearable @input="filterExamItems" style="width: 220px">
                    <template #prefix>
                      <el-icon><i class="el-icon-search"></i></el-icon>
                    </template>
                  </el-input>
                  <el-select v-model="selectedCategory" placeholder="项目分类" style="width: 220px; margin-left: 10px" @change="filterExamItems">
                    <el-option label="全部分类" value="" />
                    <el-option v-for="category in examCategories" :key="category.value" :label="category.label" :value="category.value" />
                  </el-select>
                </div>

                <div class="exam-items-wrapper">
                  <div class="exam-items-list">
                    <div class="section-title">可选体检项目</div>
                    <el-table
                      :data="filteredExamItems"
                      style="width: 100%"
                      height="300"
                      border
                      stripe
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                      }"
                    >
                      <el-table-column type="selection" width="55" @selection-change="handleSelectionChange" />
                      <el-table-column prop="code" label="项目编码" width="90" />
                      <el-table-column prop="name" label="项目名称" width="150" />
                      <el-table-column prop="categoryName" label="项目分类" width="100" />
                      <el-table-column prop="price" label="单价(元)" width="80" align="right">
                        <template #default="scope">
                          <span class="price-value">{{ scope.row.price.toFixed(2) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="项目说明" show-overflow-tooltip />
                      <el-table-column fixed="right" label="操作" width="80" align="center">
                        <template #default="scope">
                          <el-button link type="primary" @click="addExamItem(scope.row)">添加</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div class="selected-items-list">
                    <div class="section-title">已选择项目</div>
                    <div class="selected-header">
                      <span class="selected-title">已选择项目</span>
                      <span class="selected-count">共 {{ selectedExamItems.length }} 项</span>
                      <span class="selected-price">总价: {{ totalPrice }} 元</span>
                    </div>
                    <el-table
                      :data="selectedExamItems"
                      style="width: 100%"
                      height="300"
                      border
                      stripe
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                      }"
                    >
                      <el-table-column prop="code" label="项目编码" width="90" />
                      <el-table-column prop="name" label="项目名称" width="150" />
                      <el-table-column prop="categoryName" label="项目分类" width="100" />
                      <el-table-column label="单价(元)" width="120" align="right">
                        <template #default="scope">
                          <el-input-number
                            v-model="scope.row.price"
                            :min="0"
                            :precision="2"
                            size="small"
                            style="width: 100px"
                            @change="updateTotalPrice"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="80" align="center">
                        <template #default="scope">
                          <el-button link type="danger" @click="removeExamItem(scope.row)">移除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="加项套餐" name="packagePlus">
                <div class="search-box">
                  <el-input v-model="searchPackagePlusKeyword" placeholder="搜索加项套餐" clearable @input="filterPackagePlus" style="width: 220px">
                    <template #prefix>
                      <el-icon><i class="el-icon-search"></i></el-icon>
                    </template>
                  </el-input>
                  <el-select v-model="selectedGender" placeholder="适用性别" style="width: 220px; margin-left: 10px" @change="filterPackagePlus">
                    <el-option label="全部性别" value="" />
                    <el-option label="不限" value="0" />
                    <el-option label="男" value="1" />
                    <el-option label="女" value="2" />
                  </el-select>
                </div>

                <div class="exam-items-wrapper">
                  <div class="exam-items-list">
                    <div class="section-title">可选加项套餐</div>
                    <el-table
                      :data="filteredPackagePlus"
                      style="width: 100%"
                      height="300"
                      border
                      stripe
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                      }"
                    >
                      <el-table-column prop="name" label="加项套餐名称" width="150" />
                      <el-table-column label="适用性别" width="80" align="center">
                        <template #default="scope">
                          <el-tag
                            :type="scope.row.gender === 1 ? 'primary' : scope.row.gender === 2 ? 'success' : 'info'"
                            effect="plain"
                            size="small"
                          >
                            {{ getGenderText(scope.row.gender) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="price" label="价格(元)" width="80" align="right">
                        <template #default="scope">
                          <span class="price-value">{{ scope.row.price.toFixed(2) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="itemCount" label="包含项目数" width="80" align="center" />
                      <el-table-column fixed="right" label="操作" width="150" align="center">
                        <template #default="scope">
                          <el-button link type="primary" @click="addPackagePlus(scope.row)">添加</el-button>
                          <el-button link type="info" @click="viewPackagePlusDetail(scope.row)">查看详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div class="selected-items-list">
                    <div class="section-title">已选择加项套餐</div>
                    <div class="selected-header">
                      <span class="selected-title">已选择加项套餐</span>
                      <span class="selected-count">共 {{ selectedPackagePlus.length }} 项</span>
                      <span class="selected-price">总价: {{ totalPackagePlusPrice }} 元</span>
                    </div>
                    <el-table
                      :data="selectedPackagePlus"
                      style="width: 100%"
                      height="300"
                      border
                      stripe
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                      }"
                    >
                      <el-table-column prop="name" label="加项套餐名称" width="150" />
                      <el-table-column label="适用性别" width="80" align="center">
                        <template #default="scope">
                          <el-tag
                            :type="scope.row.gender === 1 ? 'primary' : scope.row.gender === 2 ? 'success' : 'info'"
                            effect="plain"
                            size="small"
                          >
                            {{ getGenderText(scope.row.gender) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="价格(元)" width="120" align="right">
                        <template #default="scope">
                          <el-input-number
                            v-model="scope.row.price"
                            :min="0"
                            :precision="2"
                            size="small"
                            style="width: 100px"
                            @change="updateTotalPackagePlusPrice"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column prop="itemCount" label="包含项目数" width="80" align="center" />
                      <el-table-column fixed="right" label="操作" width="150" align="center">
                        <template #default="scope">
                          <el-button link type="danger" @click="removePackagePlus(scope.row)">移除</el-button>
                          <el-button link type="info" @click="viewPackagePlusDetail(scope.row)">查看详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="加项包" name="additionPackage">
                <div class="search-box">
                  <el-input
                    v-model="searchAdditionPackageKeyword"
                    placeholder="搜索加项包"
                    clearable
                    @input="filterAdditionPackage"
                    style="width: 220px"
                  >
                    <template #prefix>
                      <el-icon><i class="el-icon-search"></i></el-icon>
                    </template>
                  </el-input>
                  <el-select
                    v-model="selectedAdditionGender"
                    placeholder="适用性别"
                    style="width: 220px; margin-left: 10px"
                    @change="filterAdditionPackage"
                  >
                    <el-option label="全部性别" value="" />
                    <el-option label="不限" value="0" />
                    <el-option label="男" value="1" />
                    <el-option label="女" value="2" />
                  </el-select>
                </div>

                <div class="exam-items-wrapper">
                  <div class="exam-items-list">
                    <div class="section-title">可选加项包</div>
                    <el-table
                      :data="filteredAdditionPackages"
                      style="width: 100%"
                      height="300"
                      border
                      stripe
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                      }"
                    >
                      <el-table-column prop="name" label="加项包名称" width="150" />
                      <el-table-column label="适用性别" width="80" align="center">
                        <template #default="scope">
                          <el-tag
                            :type="scope.row.gender === 1 ? 'primary' : scope.row.gender === 2 ? 'success' : 'info'"
                            effect="plain"
                            size="small"
                          >
                            {{ getGenderText(scope.row.gender) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="price" label="价格(元)" width="80" align="right">
                        <template #default="scope">
                          <span class="price-value">{{ scope.row.price.toFixed(2) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="150" align="center">
                        <template #default="scope">
                          <el-button link type="primary" @click="addAdditionPackage(scope.row)">添加</el-button>
                          <el-button link type="info" @click="viewAdditionPackageDetail(scope.row)">查看详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div class="selected-items-list">
                    <div class="section-title">已选择加项包</div>
                    <div class="selected-header">
                      <span class="selected-title">已选择加项包</span>
                      <span class="selected-count">共 {{ selectedAdditionPackages.length }} 项</span>
                      <span class="selected-price">总价: {{ totalAdditionPackagePrice }} 元</span>
                    </div>
                    <el-table
                      :data="selectedAdditionPackages"
                      style="width: 100%"
                      height="300"
                      border
                      stripe
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold'
                      }"
                    >
                      <el-table-column prop="name" label="加项包名称" width="150" />
                      <el-table-column label="适用性别" width="80" align="center">
                        <template #default="scope">
                          <el-tag
                            :type="scope.row.gender === 1 ? 'primary' : scope.row.gender === 2 ? 'success' : 'info'"
                            effect="plain"
                            size="small"
                          >
                            {{ getGenderText(scope.row.gender) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="价格(元)" width="120" align="right">
                        <template #default="scope">
                          <el-input-number
                            v-model="scope.row.price"
                            :min="0"
                            :precision="2"
                            size="small"
                            style="width: 100px"
                            @change="updateTotalAdditionPackagePrice"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="150" align="center">
                        <template #default="scope">
                          <el-button link type="danger" @click="removeAdditionPackage(scope.row)">移除</el-button>
                          <el-button link type="info" @click="viewAdditionPackageDetail(scope.row)">查看详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <!-- 保存按钮 -->
            <div class="action-buttons">
              <el-button
                type="primary"
                @click="saveItems"
                :disabled="
                  !selectedPackage.id || (selectedExamItems.length === 0 && selectedPackagePlus.length === 0 && selectedAdditionPackages.length === 0)
                "
              >
                保存到套餐
              </el-button>
              <el-button @click="clearAllSelectedItems">清空已选项目</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 定义体检项目类型
interface ExamItem {
  id: string;
  code: string;
  name: string;
  categoryId: string;
  categoryName: string;
  price: number;
  description: string;
}

// 定义体检套餐类型
interface Package {
  id: string;
  name: string;
  type: string;
  gender: number;
  minAge: number;
  maxAge: number;
  price: number;
  examItems?: ExamItem[];
}

// 定义加项套餐类型
interface PackagePlus {
  id: string;
  name: string;
  gender: number;
  price: number;
  itemCount: number;
  examItems?: ExamItem[];
}

// 定义加项包类型
interface AdditionPackage {
  id: string;
  name: string;
  gender: number;
  price: number;
  examItems?: ExamItem[];
}

// 搜索表单
const searchForm = reactive({
  name: "",
  type: ""
});

// 套餐类型选项
const packageTypeOptions = [
  { label: "常规体检", value: "1" },
  { label: "入职体检", value: "2" },
  { label: "婚前体检", value: "3" },
  { label: "孕前体检", value: "4" },
  { label: "老年体检", value: "5" },
  { label: "儿童体检", value: "6" },
  { label: "妇科体检", value: "7" },
  { label: "心脑血管体检", value: "8" },
  { label: "肿瘤筛查", value: "9" },
  { label: "其他", value: "10" }
];

// 获取套餐类型标签
const getPackageTypeLabel = (type: string) => {
  const option = packageTypeOptions.find(item => item.value === type);
  return option ? option.label : "未知类型";
};

// 获取性别文本
const getGenderText = (gender: number) => {
  switch (gender) {
    case 0:
      return "不限";
    case 1:
      return "男";
    case 2:
      return "女";
    default:
      return "未知";
  }
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);

// 套餐列表
const packageList = ref<Package[]>([]);

// 选中的套餐
const selectedPackage = ref<Package>({
  id: "",
  name: "",
  type: "",
  gender: 0,
  minAge: 0,
  maxAge: 0,
  price: 0
});

// 搜索套餐
const searchPackages = () => {
  loadPackageList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.name = "";
  searchForm.type = "";
  loadPackageList();
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  loadPackageList();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadPackageList();
};

// 加载套餐列表
const loadPackageList = () => {
  // 这里应该调用API获取数据
  // 模拟加载数据
  setTimeout(() => {
    // 生成模拟数据
    const mockData: Package[] = [];
    for (let i = 1; i <= 20; i++) {
      mockData.push({
        id: `${i}`,
        name: `体检套餐${i}`,
        type: `${(i % 10) + 1}`,
        gender: i % 3, // 0: 不限, 1: 男, 2: 女
        minAge: 18,
        maxAge: i % 5 === 0 ? 0 : 60 + i,
        price: Math.floor(Math.random() * 1000) + 200,
        examItems: []
      });
    }

    // 过滤数据
    let filteredData = [...mockData];
    if (searchForm.name) {
      filteredData = filteredData.filter(item => item.name.includes(searchForm.name));
    }
    if (searchForm.type) {
      filteredData = filteredData.filter(item => item.type === searchForm.type);
    }

    // 计算分页
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    packageList.value = filteredData.slice(start, end);
    total.value = filteredData.length;
  }, 300);
};

// 选择套餐
const selectPackage = (row: Package) => {
  selectedPackage.value = { ...row };
  ElMessage.success(`已选择套餐: ${row.name}`);
};

// 处理套餐行点击
const handlePackageSelect = (row: Package) => {
  selectPackage(row);
};

// 清除选中的套餐
const clearSelectedPackage = () => {
  selectedPackage.value = {
    id: "",
    name: "",
    type: "",
    gender: 0,
    minAge: 0,
    maxAge: 0,
    price: 0
  };
  selectedExamItems.value = [];
};

// 体检项目分类
const examCategories = [
  { label: "实验室检查", value: "1" },
  { label: "影像学检查", value: "2" },
  { label: "内科检查", value: "3" },
  { label: "外科检查", value: "4" },
  { label: "妇科检查", value: "5" },
  { label: "其他检查", value: "6" }
];

// 模拟体检项目数据
const examItems = reactive([
  {
    id: "1",
    code: "XY001",
    name: "血常规",
    categoryId: "1",
    categoryName: "实验室检查",
    price: 35,
    description: "检查血液中的红细胞、白细胞、血小板等指标"
  },
  { id: "2", code: "XY002", name: "尿常规", categoryId: "1", categoryName: "实验室检查", price: 25, description: "检查尿液中的各种指标" },
  { id: "3", code: "XY003", name: "肝功能", categoryId: "1", categoryName: "实验室检查", price: 60, description: "检查肝脏功能相关指标" },
  { id: "4", code: "XY004", name: "肾功能", categoryId: "1", categoryName: "实验室检查", price: 60, description: "检查肾脏功能相关指标" },
  { id: "5", code: "XY005", name: "血脂", categoryId: "1", categoryName: "实验室检查", price: 50, description: "检查血液中的脂质含量" },
  { id: "6", code: "YX001", name: "胸部X线", categoryId: "2", categoryName: "影像学检查", price: 80, description: "检查胸部器官情况" },
  { id: "7", code: "YX002", name: "腹部B超", categoryId: "2", categoryName: "影像学检查", price: 120, description: "检查腹部器官情况" },
  { id: "8", code: "YX003", name: "心电图", categoryId: "2", categoryName: "影像学检查", price: 60, description: "检查心脏电活动情况" },
  { id: "9", code: "NK001", name: "内科检查", categoryId: "3", categoryName: "内科检查", price: 40, description: "内科医生进行的常规检查" },
  { id: "10", code: "WK001", name: "外科检查", categoryId: "4", categoryName: "外科检查", price: 40, description: "外科医生进行的常规检查" },
  { id: "11", code: "FK001", name: "妇科检查", categoryId: "5", categoryName: "妇科检查", price: 60, description: "妇科医生进行的常规检查" },
  { id: "12", code: "QT001", name: "身高体重", categoryId: "6", categoryName: "其他检查", price: 10, description: "测量身高和体重" },
  { id: "13", code: "QT002", name: "血压测量", categoryId: "6", categoryName: "其他检查", price: 10, description: "测量血压" },
  { id: "14", code: "YX004", name: "颈部B超", categoryId: "2", categoryName: "影像学检查", price: 100, description: "检查颈部器官情况" },
  { id: "15", code: "YX005", name: "乳腺B超", categoryId: "2", categoryName: "影像学检查", price: 100, description: "检查乳腺情况" }
]);

// 搜索关键字
const searchKeyword = ref("");

// 选中的分类
const selectedCategory = ref("");

// 过滤后的体检项目
const filteredExamItems = computed(() => {
  let result = examItems;

  // 按分类过滤
  if (selectedCategory.value) {
    result = result.filter(item => item.categoryId === selectedCategory.value);
  }

  // 按关键字搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(
      item =>
        item.name.toLowerCase().includes(keyword) || item.code.toLowerCase().includes(keyword) || item.description.toLowerCase().includes(keyword)
    );
  }

  // 过滤掉已选择的项目
  result = result.filter(item => !selectedExamItems.value.some(selected => selected.id === item.id));

  return result;
});

// 已选择的体检项目
const selectedExamItems = ref<ExamItem[]>([]);

// 计算总价
const totalPrice = computed(() => {
  return selectedExamItems.value.reduce((sum, item) => sum + item.price, 0).toFixed(2);
});

// 更新总价
const updateTotalPrice = () => {
  // 由于使用了计算属性，这里不需要额外的逻辑
};

// 处理表格选择变化
const handleSelectionChange = (selection: ExamItem[]) => {
  // 这里可以处理多选逻辑
};

// 添加体检项目
const addExamItem = (item: ExamItem) => {
  // 检查是否已经添加过
  const exists = selectedExamItems.value.some(selected => selected.id === item.id);
  if (exists) {
    ElMessage.warning(`项目 ${item.name} 已添加`);
    return;
  }

  // 添加到已选列表
  selectedExamItems.value.push({ ...item });
  ElMessage.success(`已添加项目: ${item.name}`);
};

// 移除体检项目
const removeExamItem = (item: ExamItem) => {
  const index = selectedExamItems.value.findIndex(selected => selected.id === item.id);
  if (index !== -1) {
    selectedExamItems.value.splice(index, 1);
    ElMessage.success(`已移除项目: ${item.name}`);
  }
};

// 过滤体检项目
const filterExamItems = () => {
  // 由于使用了计算属性，这里不需要额外的逻辑
};

// 清空已选项目
const clearSelectedItems = () => {
  if (selectedExamItems.value.length === 0) {
    ElMessage.info("没有已选择的项目");
    return;
  }

  ElMessageBox.confirm("确定要清空所有已选择的项目吗？", "清空确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      selectedExamItems.value = [];
      ElMessage.success("已清空所有选择的项目");
    })
    .catch(() => {
      // 取消清空
    });
};

// 保存项目到套餐
const savePackageItems = () => {
  if (!selectedPackage.value.id) {
    ElMessage.warning("请先选择一个体检套餐");
    return;
  }

  if (selectedExamItems.value.length === 0) {
    ElMessage.warning("请至少选择一个体检项目");
    return;
  }

  // 这里应该调用API保存数据
  // 模拟保存
  setTimeout(() => {
    ElMessage.success(`已成功将 ${selectedExamItems.value.length} 个项目添加到套餐 ${selectedPackage.value.name}`);

    // 清空已选项目
    selectedExamItems.value = [];

    // 更新套餐价格（实际应用中应该由后端计算）
    selectedPackage.value.price += parseFloat(totalPrice.value);
  }, 500);
};

// 标签页相关
const activeTab = ref("examItems");

// 加项套餐相关
const searchPackagePlusKeyword = ref("");
const selectedGender = ref("");
const packagePlusList = ref<PackagePlus[]>([]);
const selectedPackagePlus = ref<PackagePlus[]>([]);

// 过滤后的加项套餐
const filteredPackagePlus = computed(() => {
  let result = packagePlusList.value;

  // 按性别过滤
  if (selectedGender.value) {
    result = result.filter(item => item.gender.toString() === selectedGender.value);
  }

  // 按关键字搜索
  if (searchPackagePlusKeyword.value) {
    const keyword = searchPackagePlusKeyword.value.toLowerCase();
    result = result.filter(item => item.name.toLowerCase().includes(keyword));
  }

  // 过滤掉已选择的加项套餐
  result = result.filter(item => !selectedPackagePlus.value.some(selected => selected.id === item.id));

  return result;
});

// 计算加项套餐总价
const totalPackagePlusPrice = computed(() => {
  return selectedPackagePlus.value.reduce((sum, item) => sum + item.price, 0).toFixed(2);
});

// 更新加项套餐总价
const updateTotalPackagePlusPrice = () => {
  // 由于使用了计算属性，这里不需要额外的逻辑
};

// 加载加项套餐列表
const loadPackagePlusList = () => {
  // 这里应该调用API获取数据
  // 模拟加载数据
  setTimeout(() => {
    // 生成模拟数据
    const mockData: PackagePlus[] = [];
    for (let i = 1; i <= 10; i++) {
      mockData.push({
        id: `plus-${i}`,
        name: `加项套餐${i}`,
        gender: i % 3, // 0: 不限, 1: 男, 2: 女
        price: Math.floor(Math.random() * 300) + 100,
        itemCount: Math.floor(Math.random() * 5) + 1,
        examItems: []
      });
    }

    packagePlusList.value = mockData;
  }, 300);
};

// 添加加项套餐
const addPackagePlus = (item: PackagePlus) => {
  // 检查是否已经添加过
  const exists = selectedPackagePlus.value.some(selected => selected.id === item.id);
  if (exists) {
    ElMessage.warning(`加项套餐 ${item.name} 已添加`);
    return;
  }

  // 添加到已选列表
  selectedPackagePlus.value.push({ ...item });
  ElMessage.success(`已添加加项套餐: ${item.name}`);
};

// 移除加项套餐
const removePackagePlus = (item: PackagePlus) => {
  const index = selectedPackagePlus.value.findIndex(selected => selected.id === item.id);
  if (index !== -1) {
    selectedPackagePlus.value.splice(index, 1);
    ElMessage.success(`已移除加项套餐: ${item.name}`);
  }
};

// 查看加项套餐详情
const viewPackagePlusDetail = (item: PackagePlus) => {
  ElMessageBox.alert(
    `<div>
      <p><strong>加项套餐名称:</strong> ${item.name}</p>
      <p><strong>适用性别:</strong> ${getGenderText(item.gender)}</p>
      <p><strong>价格:</strong> ${item.price.toFixed(2)} 元</p>
      <p><strong>包含项目数:</strong> ${item.itemCount} 项</p>
      <p><strong>包含项目:</strong> ${item.examItems?.map(i => i.name).join(", ") || "暂无详细项目信息"}</p>
    </div>`,
    "加项套餐详情",
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "确定"
    }
  );
};

// 过滤加项套餐
const filterPackagePlus = () => {
  // 由于使用了计算属性，这里不需要额外的逻辑
};

// 加项包相关
const searchAdditionPackageKeyword = ref("");
const selectedAdditionGender = ref("");
const additionPackageList = ref<AdditionPackage[]>([]);
const selectedAdditionPackages = ref<AdditionPackage[]>([]);

// 过滤后的加项包
const filteredAdditionPackages = computed(() => {
  let result = additionPackageList.value;

  // 按性别过滤
  if (selectedAdditionGender.value) {
    result = result.filter(item => item.gender.toString() === selectedAdditionGender.value);
  }

  // 按关键字搜索
  if (searchAdditionPackageKeyword.value) {
    const keyword = searchAdditionPackageKeyword.value.toLowerCase();
    result = result.filter(item => item.name.toLowerCase().includes(keyword));
  }

  // 过滤掉已选择的加项包
  result = result.filter(item => !selectedAdditionPackages.value.some(selected => selected.id === item.id));

  return result;
});

// 计算加项包总价
const totalAdditionPackagePrice = computed(() => {
  return selectedAdditionPackages.value.reduce((sum, item) => sum + item.price, 0).toFixed(2);
});

// 更新加项包总价
const updateTotalAdditionPackagePrice = () => {
  // 由于使用了计算属性，这里不需要额外的逻辑
};

// 加载加项包列表
const loadAdditionPackageList = () => {
  // 这里应该调用API获取数据
  // 模拟加载数据
  setTimeout(() => {
    // 生成模拟数据
    const mockData: AdditionPackage[] = [];
    for (let i = 1; i <= 8; i++) {
      mockData.push({
        id: `addition-${i}`,
        name: `加项包${i}`,
        gender: i % 3, // 0: 不限, 1: 男, 2: 女
        price: Math.floor(Math.random() * 200) + 50,
        examItems: []
      });
    }

    additionPackageList.value = mockData;
  }, 300);
};

// 添加加项包
const addAdditionPackage = (item: AdditionPackage) => {
  // 检查是否已经添加过
  const exists = selectedAdditionPackages.value.some(selected => selected.id === item.id);
  if (exists) {
    ElMessage.warning(`加项包 ${item.name} 已添加`);
    return;
  }

  // 添加到已选列表
  selectedAdditionPackages.value.push({ ...item });
  ElMessage.success(`已添加加项包: ${item.name}`);
};

// 移除加项包
const removeAdditionPackage = (item: AdditionPackage) => {
  const index = selectedAdditionPackages.value.findIndex(selected => selected.id === item.id);
  if (index !== -1) {
    selectedAdditionPackages.value.splice(index, 1);
    ElMessage.success(`已移除加项包: ${item.name}`);
  }
};

// 查看加项包详情
const viewAdditionPackageDetail = (item: AdditionPackage) => {
  ElMessageBox.alert(
    `<div>
      <p><strong>加项包名称:</strong> ${item.name}</p>
      <p><strong>适用性别:</strong> ${getGenderText(item.gender)}</p>
      <p><strong>价格:</strong> ${item.price.toFixed(2)} 元</p>
      <p><strong>包含项目:</strong> ${item.examItems?.map(i => i.name).join(", ") || "暂无详细项目信息"}</p>
    </div>`,
    "加项包详情",
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "确定"
    }
  );
};

// 过滤加项包
const filterAdditionPackage = () => {
  // 由于使用了计算属性，这里不需要额外的逻辑
};

// 清空所有已选项目
const clearAllSelectedItems = () => {
  if (selectedExamItems.value.length === 0 && selectedPackagePlus.value.length === 0 && selectedAdditionPackages.value.length === 0) {
    ElMessage.info("没有已选择的项目");
    return;
  }

  ElMessageBox.confirm("确定要清空所有已选择的项目、加项套餐和加项包吗？", "清空确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      selectedExamItems.value = [];
      selectedPackagePlus.value = [];
      selectedAdditionPackages.value = [];
      ElMessage.success("已清空所有选择的项目、加项套餐和加项包");
    })
    .catch(() => {
      // 取消清空
    });
};

// 保存项目、加项套餐和加项包到套餐
const saveItems = () => {
  if (!selectedPackage.value.id) {
    ElMessage.warning("请先选择一个体检套餐");
    return;
  }

  if (selectedExamItems.value.length === 0 && selectedPackagePlus.value.length === 0 && selectedAdditionPackages.value.length === 0) {
    ElMessage.warning("请至少选择一个体检项目、加项套餐或加项包");
    return;
  }

  // 这里应该调用API保存数据
  // 模拟保存
  setTimeout(() => {
    let message = "";
    let parts = [];

    if (selectedExamItems.value.length > 0) {
      parts.push(`${selectedExamItems.value.length} 个项目`);
    }

    if (selectedPackagePlus.value.length > 0) {
      parts.push(`${selectedPackagePlus.value.length} 个加项套餐`);
    }

    if (selectedAdditionPackages.value.length > 0) {
      parts.push(`${selectedAdditionPackages.value.length} 个加项包`);
    }

    // 组合消息
    if (parts.length === 1) {
      message = `已成功将 ${parts[0]}`;
    } else if (parts.length === 2) {
      message = `已成功将 ${parts[0]} 和 ${parts[1]}`;
    } else if (parts.length === 3) {
      message = `已成功将 ${parts[0]}、${parts[1]} 和 ${parts[2]}`;
    }

    message += ` 添加到套餐 ${selectedPackage.value.name}`;

    ElMessage.success(message);

    // 清空已选项目、加项套餐和加项包
    selectedExamItems.value = [];
    selectedPackagePlus.value = [];
    selectedAdditionPackages.value = [];

    // 更新套餐价格（实际应用中应该由后端计算）
    const totalItemsPrice = parseFloat(totalPrice.value);
    const totalPackagePlusValue = parseFloat(totalPackagePlusPrice.value);
    const totalAdditionValue = parseFloat(totalAdditionPackagePrice.value);
    selectedPackage.value.price += totalItemsPrice + totalPackagePlusValue + totalAdditionValue;
  }, 500);
};

// 页面加载时获取套餐列表、加项套餐列表和加项包列表
loadPackageList();
loadPackagePlusList();
loadAdditionPackageList();
</script>

<style scoped>
.package-item-container {
  padding: 20px;
}
.search-card,
.selected-package-card {
  height: calc(100vh - 60px);
  margin-bottom: 20px;
  overflow-y: auto;
}
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
.search-form {
  margin-bottom: 15px;
}
.search-form :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: bold;
}
.search-form :deep(.el-input__inner),
.search-form :deep(.el-select__inner) {
  font-size: 14px;
}
.package-list {
  margin-top: 15px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
.package-info {
  margin-bottom: 15px;
}
.price-value {
  font-weight: bold;
  color: #f56c6c;
}
.add-items-section {
  margin-top: 15px;
}
.search-box {
  display: flex;
  margin-bottom: 10px;
}
.exam-items-wrapper {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}
.exam-items-list,
.selected-items-list {
  flex: 1;
  padding: 8px;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
.section-title {
  padding-bottom: 8px;
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
}
.selected-header {
  display: flex;
  align-items: center;
  padding: 6px;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
.selected-title {
  margin-right: 12px;
  font-weight: bold;
}
.selected-count {
  margin-right: 12px;
  color: #606266;
}
.selected-price {
  margin-left: auto;
  font-weight: bold;
  color: #f56c6c;
}
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 15px;
}

/* 标签页样式 */
.item-tabs {
  margin-top: 10px;
}
.item-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
}
.item-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  font-size: 15px;
}
.item-tabs :deep(.el-tabs__item.is-active) {
  font-weight: bold;
}

/* 左右布局样式 */
.el-row {
  height: calc(100vh - 40px);
}
.el-col {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}
::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}
</style>
