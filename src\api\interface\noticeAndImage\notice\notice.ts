/**
 * @description 体检须知管理接口
 */

import { ReqPage } from "@/api";

export namespace sysNotice {
  /** 体检须知分页查询 */
  // export interface Page {
  //   titleCode: string;
  //   titleName: string;
  // }
  export interface Page extends ReqPage {
    id: string;
    typeCode: string;
    status: string;
  }

  /** 体检须知信息 */
  export interface noticeInput extends notice {
    content: string;
  }
  export interface noticeTable extends notice {
    label: string;
    value: string;
    styleColor: boolean;
  }

  export interface notice {
    id: string;
    typeCode: string;
    content: string;
    status: string;
  }
}
