// 请求响应参数（不包含data）
export interface Result {
  code: string | number;
  msg: string;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  list: T[];
  pageNum: number;
  pageSize: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  /** 页码 */
  pageNum: number;
  /** 数量 */
  pageSize: number;
  /** 排序字段 */
  sortField?: string;
  /** 排序方式 */
  sortOrder?: string;
  /** 关键字 */
  searchKey?: string;
}

/** id请求参数 */
export interface ReqId {
  /** id */
  id: number | string;
}

export interface SelectOption {
  label: string;
  value: number;
}

export * from "./sys";
export * from "./userCenter";
export * from "./upload";
export * from "./sourceManagement";
export * from "./groupManagement";
export * from "./projectManagement";
export * from "./individualManagement";
export * from "./packageManagement";
export * from "./members";
export * from "./noticeAndImage";
