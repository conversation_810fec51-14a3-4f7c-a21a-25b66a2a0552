<!-- 表单-->
<template>
  <form-container v-model="visible" :title="`${sysRoleProps.opt}角色`" form-size="800px" @close="onClose">
    <el-form
      ref="sysRoleFormRef"
      :rules="rules"
      :disabled="sysRoleProps.disabled"
      :model="sysRoleProps.record"
      :hide-required-asterisk="sysRoleProps.disabled"
      label-width="auto"
      label-suffix=" :"
    >
      <s-form-item label="角色名称" prop="name">
        <s-input v-model="sysRoleProps.record.name"></s-input>
      </s-form-item>
      <s-form-item label="角色编码" prop="code">
        <s-input
          v-model="sysRoleProps.record.code"
          :disabled="sysRoleProps.record.id != undefined"
          placeholder="请填写角色编码,不填则为随机值"
        ></s-input>
      </s-form-item>
      <s-form-item label="状态" prop="status">
        <s-radio-group v-model="sysRoleProps.record.status" :options="statusOptions" button />
      </s-form-item>
      <s-form-item label="排序" prop="sortCode">
        <el-slider v-model="sysRoleProps.record.sortCode" show-input :min="1" />
      </s-form-item>

      <s-form-item label="关联院区">
        <el-select v-model="orgChoose" filterable multiple placeholder="Select" style="width: 400px">
          <el-option-group v-for="group in orgOptions" :key="group.label" :label="group.label">
            <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
          </el-option-group>
        </el-select>
      </s-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose"> 取消 </el-button>
      <el-button v-auth="userButtonCode.操作角色" v-show="!sysRoleProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
    </template>
  </form-container>
</template>

<script setup lang="ts">
import { SysRole, sysRoleApi, userButtonCode } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance, ElMessage } from "element-plus";
import { useDictStore, useUserStore } from "@/stores/modules";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
const userStore = useUserStore(); //用户信息
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS); // 通用状态选项

const orgChoose = ref<string[]>([]);
// 表单参数
const sysRoleProps = reactive<FormProps.Base<SysRole.SysRoleInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 组织机构列表
let orgOptions: { label: string; options: { label: string; value: string }[] | undefined }[];

// 表单验证规则
const rules = reactive({
  name: [required("请输入角色名称")],
  status: [required("请选择状态")]
});

onMounted(() => {
  // 获取Apis
  getOrgList();
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<SysRole.SysRoleInfo>) {
  Object.assign(sysRoleProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    sysRoleProps.record.sortCode = 99;
    sysRoleProps.record.status = statusOptions[0].value;
  }
  orgChoose.value = (sysRoleProps.record.extJson || "").split(",").filter(Boolean);
  visible.value = true; //显示表单
}

// 提交数据（新增/编辑）
const sysRoleFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  sysRoleFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    sysRoleProps.record.extJson = (orgChoose.value || []).filter(Boolean).join(",");
    if (!sysRoleProps.record.extJson) {
      ElMessage.warning("请选择院区，必须选择一个以上");
      return;
    }
    await sysRoleApi
      .submitForm(sysRoleProps.record, sysRoleProps.record.id != undefined)
      .then(() => {
        sysRoleProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/**菜单Apis */
function getOrgList() {
  orgOptions = [];
  var grouped: any = {};
  userStore.orgList.forEach(function (item) {
    // 为每个医院生成一个唯一的键，这里使用 orgCode
    var key = item.orgName;
    // 如果 grouped 对象中还没有该医院的键，则初始化一个新的对象
    if (!grouped[key]) {
      grouped[key] = {
        label: item.orgName,
        options: []
      };
    }
    // 将该医院下的区域信息添加到 areas 数组中
    grouped[key].options.push({
      label: item.orgName + "-" + item.areaName,
      value: item.orgCode
    });
  });
  orgOptions = Object.values(grouped);
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}
// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped></style>
