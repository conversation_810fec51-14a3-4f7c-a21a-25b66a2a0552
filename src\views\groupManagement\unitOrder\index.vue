<template>
  <div class="unit-order-container">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span class="title">团检单位人员订单查询</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" ref="searchFormRef" :inline="true" class="search-form">
        <el-form-item label="单位">
          <el-select v-model="searchForm.unit" filterable clearable placeholder="请选择单位" style="width: 220px">
            <el-option v-for="item in unitOptions" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="体检日期">
          <div style="display: flex; gap: 5px; width: 260px">
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="开始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 130px"
            >
            </el-date-picker>
            <span style="line-height: 32px">至</span>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 130px"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span class="title">团检单位人员订单列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="exportOrders">导出订单</el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="personList"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#303133',
          fontWeight: 'bold'
        }"
        :loading="loading"
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="gender" label="性别" width="60">
          <template #default="scope">
            {{ scope.row.gender === 1 ? "男" : "女" }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="60" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="unitName" label="单位名称" width="150" show-overflow-tooltip />
        <el-table-column prop="packageName" label="体检套餐" width="150" show-overflow-tooltip />
        <el-table-column prop="checkupDate" label="体检日期" width="120" />
        <el-table-column prop="batchNumber" label="批次号" width="120" />
        <el-table-column prop="validUntil" label="有效期至" width="120" />
        <el-table-column prop="checkupStatus" label="体检状态" width="100">
          <template #default="scope">
            <el-tag :type="getCheckupStatusType(scope.row.checkupStatus)">
              {{ getCheckupStatusText(scope.row.checkupStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payStatus" label="支付状态" width="100">
          <template #default="scope">
            <el-tag :type="getPayStatusType(scope.row.payStatus)">
              {{ getPayStatusText(scope.row.payStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="280">
          <template #default="scope">
            <el-button type="primary" link @click="viewPersonDetail(scope.row)">查看详情</el-button>
            <el-button type="success" link @click="editPerson(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="cancelCheckup(scope.row)">取消预约</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 人员详情对话框 -->
    <el-dialog title="体检人员详情" v-model="detailDialogVisible" width="80%" :close-on-click-modal="false">
      <el-descriptions title="人员基本信息" :column="3" border>
        <el-descriptions-item label="姓名">{{ currentPerson.name }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ currentPerson.gender === 1 ? "男" : "女" }}</el-descriptions-item>
        <el-descriptions-item label="年龄">{{ currentPerson.age }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ currentPerson.phone }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ currentPerson.idCard }}</el-descriptions-item>
        <el-descriptions-item label="单位名称">{{ currentPerson.unitName }}</el-descriptions-item>
        <el-descriptions-item label="体检套餐">{{ currentPerson.packageName }}</el-descriptions-item>
        <el-descriptions-item label="体检日期">{{ currentPerson.checkupDate }}</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ currentPerson.batchNumber }}</el-descriptions-item>
        <el-descriptions-item label="有效期至">{{ currentPerson.validUntil }}</el-descriptions-item>
        <el-descriptions-item label="体检状态">
          <el-tag :type="getCheckupStatusType(currentPerson.checkupStatus)">
            {{ getCheckupStatusText(currentPerson.checkupStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <el-tag :type="getPayStatusType(currentPerson.payStatus)">
            {{ getPayStatusText(currentPerson.payStatus) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <div class="detail-title">体检项目列表</div>
      <el-table :data="currentPerson.checkupItems || []" border stripe style="width: 100%">
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column prop="name" label="项目名称" width="150" />
        <el-table-column prop="department" label="检查科室" width="120" />
        <el-table-column prop="status" label="检查状态" width="100">
          <template #default="scope">
            <el-tag :type="getItemStatusType(scope.row.status)">
              {{ getItemStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="检查结果" min-width="200" show-overflow-tooltip />
        <el-table-column prop="checkTime" label="检查时间" width="180" />
        <el-table-column prop="doctor" label="检查医生" width="100" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance } from "element-plus";

// 搜索表单
const searchFormRef = ref<FormInstance>();
const searchForm = reactive({
  name: "",
  phone: "",
  unit: "",
  startDate: "",
  endDate: ""
});

// 表格数据
const loading = ref(false);
const personList = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 单位选项
const unitOptions = ref<any[]>([]);

// 当前选中的人员
const currentPerson = ref<any>({});
const detailDialogVisible = ref(false);

// 初始化数据
onMounted(() => {
  initUnitOptions();
  fetchOrderList();
});

// 初始化单位选项
const initUnitOptions = () => {
  // 模拟数据，实际应该从API获取
  unitOptions.value = Array.from({ length: 20 }, (_, index) => ({
    value: `unit_${index + 1}`,
    label: `测试单位${index + 1}`
  }));
};

// 获取人员列表
const fetchOrderList = () => {
  loading.value = true;

  // 模拟API请求延迟
  setTimeout(() => {
    // 模拟数据，实际应该从API获取
    const mockPersons = [];

    // 生成20个单位，每个单位10-50个人员
    for (let unitIndex = 0; unitIndex < 20; unitIndex++) {
      const unitName = `测试单位${unitIndex + 1}`;
      const personCount = 10 + (unitIndex % 40);

      for (let personIndex = 0; personIndex < personCount; personIndex++) {
        // 生成随机日期（最近30天内）
        const date = new Date();
        date.setDate(date.getDate() - ((unitIndex + personIndex) % 30));
        const checkupDate = date.toISOString().split("T")[0];

        // 计算有效期（体检日期后一年）
        const validDate = new Date(checkupDate);
        validDate.setFullYear(validDate.getFullYear() + 1);
        const validUntil = validDate.toISOString().split("T")[0];

        // 生成随机体检项目
        const checkupItems = Array.from({ length: 5 + (personIndex % 10) }, (_, itemIndex) => {
          const itemStatus = (personIndex + itemIndex) % 4; // 0: 未检查, 1: 已预约, 2: 已检查, 3: 已出结果
          const checkTime = itemStatus >= 2 ? new Date(date).toISOString().replace("T", " ").substring(0, 19) : "";

          return {
            id: `item_${unitIndex}_${personIndex}_${itemIndex}`,
            name: `体检项目${(itemIndex % 20) + 1}`,
            department: `科室${(itemIndex % 8) + 1}`,
            status: itemStatus,
            result: itemStatus === 3 ? `正常范围内` : "",
            checkTime,
            doctor: itemStatus >= 2 ? `医生${(itemIndex % 10) + 1}` : ""
          };
        });

        mockPersons.push({
          id: `person_${unitIndex}_${personIndex}`,
          name: `员工${personIndex + 1}`,
          gender: personIndex % 2 === 0 ? 1 : 2,
          age: 20 + (personIndex % 40),
          phone: `1390000${10000 + personIndex}`,
          idCard: `11010119900101${1000 + personIndex}`,
          unitName,
          packageName: `体检套餐${(personIndex % 5) + 1}`,
          checkupDate,
          batchNumber: `G${2023}${(personIndex % 50) + 1}`,
          validUntil,
          checkupStatus: personIndex % 4, // 0: 未体检, 1: 已预约, 2: 已体检, 3: 已出报告
          payStatus: personIndex % 3, // 0: 未支付, 1: 部分支付, 2: 已支付
          checkupItems
        });
      }
    }

    // 根据搜索条件过滤
    let filteredPersons = filterPersons(mockPersons);

    // 分页
    const startIndex = (currentPage.value - 1) * pageSize.value;
    const endIndex = startIndex + pageSize.value;
    personList.value = filteredPersons.slice(startIndex, endIndex);
    total.value = filteredPersons.length;

    loading.value = false;
  }, 500);
};

// 根据搜索条件过滤人员
const filterPersons = (persons: any[]) => {
  return persons.filter(person => {
    // 按单位名称筛选
    if (searchForm.unit && !person.unitName.includes(searchForm.unit)) {
      return false;
    }

    // 按姓名筛选
    if (searchForm.name && !person.name.includes(searchForm.name)) {
      return false;
    }

    // 按手机号筛选
    if (searchForm.phone && !person.phone.includes(searchForm.phone)) {
      return false;
    }

    // 按日期范围筛选
    if (searchForm.startDate && searchForm.endDate) {
      if (person.checkupDate < searchForm.startDate || person.checkupDate > searchForm.endDate) {
        return false;
      }
    }

    return true;
  });
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchOrderList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  currentPage.value = 1;
  fetchOrderList();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchOrderList();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchOrderList();
};

// 查看人员详情
const viewPersonDetail = (person: any) => {
  currentPerson.value = { ...person };
  detailDialogVisible.value = true;
};

// 编辑人员
const editPerson = (person: any) => {
  ElMessage.info(`编辑人员 ${person.name}，功能开发中...`);
};

// 取消预约
const cancelCheckup = (person: any) => {
  if (person.checkupStatus === 0) {
    ElMessage.warning("该人员尚未预约体检");
    return;
  }

  ElMessageBox.confirm(`确定要取消 ${person.name} 的体检预约吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      // 模拟取消操作，实际应该调用API
      ElMessage.success("体检预约已取消");
      person.checkupStatus = 0; // 设置为未体检
    })
    .catch(() => {
      // 取消操作
    });
};

// 导出订单
const exportOrders = () => {
  ElMessage.info("导出人员订单功能开发中...");
};

// 获取支付状态文本
const getPayStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "未支付",
    1: "部分支付",
    2: "已支付"
  };
  return statusMap[status] || "未知状态";
};

// 获取支付状态标签类型
const getPayStatusType = (status: number): "success" | "warning" | "info" | "primary" | "danger" => {
  const typeMap: Record<number, "success" | "warning" | "info" | "primary" | "danger"> = {
    0: "danger",
    1: "warning",
    2: "success"
  };
  return typeMap[status] || "info";
};

// 获取体检状态文本
const getCheckupStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "未体检",
    1: "已预约",
    2: "已体检",
    3: "已出报告"
  };
  return statusMap[status] || "未知状态";
};

// 获取体检状态标签类型
const getCheckupStatusType = (status: number): "success" | "warning" | "info" | "primary" | "danger" => {
  const typeMap: Record<number, "success" | "warning" | "info" | "primary" | "danger"> = {
    0: "info",
    1: "warning",
    2: "success",
    3: "primary"
  };
  return typeMap[status] || "info";
};

// 获取体检项目状态文本
const getItemStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "未检查",
    1: "已预约",
    2: "已检查",
    3: "已出结果"
  };
  return statusMap[status] || "未知状态";
};

// 获取体检项目状态标签类型
const getItemStatusType = (status: number): "success" | "warning" | "info" | "primary" | "danger" => {
  const typeMap: Record<number, "success" | "warning" | "info" | "primary" | "danger"> = {
    0: "info",
    1: "warning",
    2: "success",
    3: "primary"
  };
  return typeMap[status] || "info";
};
</script>

<style scoped lang="scss">
.unit-order-container {
  padding: 20px;
  .search-card {
    margin-bottom: 20px;
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 18px;
      font-weight: bold;
    }
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  .detail-title {
    padding-left: 10px;
    margin: 20px 0 10px;
    font-size: 16px;
    font-weight: bold;
    border-left: 4px solid #409eff;
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }
  ::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}
</style>
