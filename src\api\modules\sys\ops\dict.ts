/**
 * @description 系统字典
 */

import { SysDict, ReqId, ResPage } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/dict/");

const sysDictApi = {
  /** 获取字典树 */
  tree() {
    return http.get<SysDict.DictTree[]>("tree", {}, { loading: false });
  },
  /** 获取字典分页 */
  page(params: SysDict.Page) {
    return http.get<ResPage<SysDict.DictInfo>>("page", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除单页 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  }
};

export { sysDictApi };
