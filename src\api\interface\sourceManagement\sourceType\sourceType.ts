/**
 * @description 号源类型接口
 */

export namespace SourceType {
  /** 号源类型信息 */
  export interface SourceTypeInfo {
    id: string;
    sourceTypeName: string;
    sourceTypeTable: string;
    sourceTypeParentId: string;
  }
  /** 号源类型信息 */
  export interface SourceTypeDetails {
    timeSlotEntryID: string;
    timeSlotName: string;
    startTime: string;
    endTime: string;
    timePeriod: string;
    sourceTypeName: string;
    statu: string;
  }
}
