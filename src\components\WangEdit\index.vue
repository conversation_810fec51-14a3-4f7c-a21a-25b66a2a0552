<template>
  <div style="border: 1px solid #cccccc">
    <Toolbar
      style="border-bottom: 1px solid #cccccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
    />
    <Editor
      style="height: 300px; overflow-y: hidden;"
      v-model="editorValue"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<script setup lang="ts">
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IDomEditor, IToolbarConfig } from '@wangeditor/editor'

// 定义props和emits
const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'editor-created', editor: IDomEditor): void
}>()

// 编辑器实例，必须用shallowRef
const editorRef = shallowRef<IDomEditor>()
const editorValue = ref(props.modelValue)

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  // 可配置工具栏项
  excludeKeys: [ 'insertTable','fullScreen','codeBlock','insertImage','uploadImage','uploadVideo','insertVideo'],
 
}



// 组件销毁时，及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

// 编辑器创建回调
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
  emit('editor-created', editor)
}

// 内容变化回调
const handleChange = (editor: IDomEditor) => {
  const html = editor.getHtml()
  editorValue.value = html
  emit('update:modelValue', html)
}

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== editorRef.value?.getHtml()) {
    editorRef.value?.setHtml(newVal)
  }
})
</script>