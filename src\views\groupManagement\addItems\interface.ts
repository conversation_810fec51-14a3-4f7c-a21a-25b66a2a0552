/*
 * @Author: Reaper
 * @Date: 2025-06-09 09:59:12
 * @LastEditors:
 * @LastEditTime: 2025-06-10 17:21:51
 * @Description: 请填写简介
 */
// 单位套餐相关接口定义

// 单位套餐信息
export interface UnitPackage {
  clusCode: string;
  clusName: string;
  companyCode: string;
  companyTimes: number;
}

//单位体检次数
export interface CompanyTimesUI {
  companyCode: string;
  companyTimes: number;
  beginDate: Date;
  endDate: Date;
  onLineEndDate: Date;
}

// 套餐项目信息
export interface PackageItem {
  clusCode: string;
  combCode: string;
  combName: string;
  price: number;
}

// 搜索表单
export interface SearchForm {
  keyword: string; // 单位名称或编码
  checkupTimes: string; // 体检次数
}

// 分页信息
export interface Pagination {
  currentPage: number;
  pageSize: number;
  total: number;
}

// 套餐表单
export interface PackageForm {
  id: string;
  unitName: string;
  unitCode: string;
  checkupTimes: string;
  packageName: string;
  packagePrice: number;
  status: number;
  description: string;
  items: PackageItem[];
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应类型
export interface PageResponse<T = any> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}
