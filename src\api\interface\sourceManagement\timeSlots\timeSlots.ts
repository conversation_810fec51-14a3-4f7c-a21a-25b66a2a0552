/**
 * @description 时段管理接口
 */

import { ReqPage } from "@/api/interface";

export namespace TimeSlots {
  /** 时段信息 */
  export interface TimeSlotsInfo {
    id: string;
    timeSlotName: string;
    startTime: string;
    endTime: string;
    timePeriod: string;
  }
  /**指定时段信息*/
  export interface SpecifiedTimeSlotInfo extends TimeSlotsInfo {
    sourceTypeName: string;
    statu: string;
    timeSlotEntryID: string;
  }
}
