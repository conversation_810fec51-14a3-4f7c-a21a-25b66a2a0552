/**
 * @description 项目分类理接口
 */
import { ProjectClassification } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/admin/basiccode/");
const httpOther = moduleRequest("/admin/syncbusiness/");

const projectClassificationApi = {
  /** 获取项目分类 */
  page() {
    return http.get<ProjectClassification.ProjectClassificationInfo[]>("getcodeitemcls");
  },
  /** 新增/修改项目分类 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "editcodeitemcls" : "addcodeitemcls", params);
  },
  /** 删除项目分类 */
  delete(params: {}) {
    return http.post("deletecodeitemcls", params);
  },
  /** 同步项目分类 */
  synchronization() {
    return httpOther.post("synccodeitemcls", {});
  }
};

export { projectClassificationApi };
