import { companyManagementApi } from "@/api";
import { usePagination } from "../usePagination";
import { useCompanyTimes } from "../useCompanyTimes";
import { usePackageQuery } from "../usePackageQuery";

interface Package {
  companyCode: string;
  companyTimes: number;
  clusCode: string;
  clusterName: string;
}
type Packages = Package[];
export const usePackages = () => {
  const packages = ref<Packages>([]);

  const { pagination } = usePagination();
  const { companyTimes } = useCompanyTimes();
  const { packageQuery } = usePackageQuery();

  async function init(unitId: string, times: number) {
    packages.value = await searchPackages(unitId, times);
  }

  async function searchPackages(unitId: string, times: number) {
    const { data } = await companyManagementApi.getCompanyCluster(unitId, times);
    pagination.value.total = data.length;
    return data;
  }

  return {
    packages,
    init,
    searchPackages
  };
};
