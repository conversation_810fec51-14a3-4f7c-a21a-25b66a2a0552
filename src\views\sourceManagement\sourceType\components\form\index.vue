<template>
  <div>
    <form-container v-model="visible" title="关联时段" form-size="600px" @close="onClose">
      <div class="form">
        <el-transfer v-model="selectTimeSlot" :data="timeSlotList" :titles="['时段列表', '已选时段']" />
      </div>
      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!sourceTypeProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { TimeSlots, timeSlotsApi, sourceTypeApi } from "@/api";
import { FormOptEnum } from "@/enums";

const visible = ref(false); //是否显示表单

// 表单参数
const sourceTypeProps = reactive<FormProps.Base<TimeSlots.TimeSlotsInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});
const timeSlotList = ref<{ label: string; key: string }[]>([]); //时段列表
const selectTimeSlot = ref([]); //选中的时段列表

/**
 * 打开表单
 * @param props 表单参数
 */
async function onOpen(props: FormProps.Base<TimeSlots.TimeSlotsInfo>) {
  Object.assign(sourceTypeProps, props); //合并参数
  await getTimeSlotsList();
  await getDetails();
  visible.value = true; //显示表单
}

/** 提交 */
async function handleSubmit() {
  if (selectTimeSlot.value.length == 0) {
    return ElMessage.error("请选择时段");
  }
  const params = { sourceTypeID: sourceTypeProps.record.id, timeSlotEntryID: selectTimeSlot.value };
  await sourceTypeApi
    .submitForm(params)
    .then(() => {
      ElMessage.success("保存成功");
      sourceTypeProps.successful!(); //调用父组件的successful方法
    })
    .finally(() => {
      onClose();
    });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 获取时段列表
async function getTimeSlotsList() {
  const { data } = await timeSlotsApi.page();
  timeSlotList.value = data.map(item => ({ label: item.timeSlotName, key: item.id }));
}

// 获取号源类型详情
async function getDetails() {
  const { data } = await sourceTypeApi.getSourceTypeDetail({ sourceTypeID: sourceTypeProps.record.id });
  if (data.length > 0) {
    selectTimeSlot.value = data.map(item => item.timeSlotEntryID);
  } else {
    selectTimeSlot.value = [];
  }
}
// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container {
  .form {
    margin-top: 20px;
  }
  :deep(.el-dialog__body) {
    margin-top: 0;
  }
  :deep(.el-drawer__body) {
    margin-top: -20px;
  }
}
</style>
