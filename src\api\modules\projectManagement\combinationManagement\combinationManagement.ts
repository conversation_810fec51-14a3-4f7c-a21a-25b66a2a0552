/**
 * @description 组合管理接口
 */
import { CombinationManagement } from "@/api/interface";
import { moduleRequest } from "@/api/request";
import { ResPage } from "@/api";
const http = moduleRequest("/admin/basiccode/");
const httpOther = moduleRequest("/admin/syncbusiness/");

const combinationManagementApi = {
  /** 组合分页 */
  page(params: CombinationManagement.Page) {
    return http.post<ResPage<CombinationManagement.CombinationInfo>>("getcodeitemcomb", params);
  },
  /** 新增/修改组合 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "editcodeitemcomb" : "addcodeitemcomb", params);
  },
  /** 删除组合 */
  delete(params: {}) {
    return http.post("deletecodeitemcomb", params);
  },
  /** 同步组合 */
  syncCombination() {
    return httpOther.post<boolean>("synccodeitemcomb", {});
  },
  /** 组合不分页 */
  getCombination() {
    return http.post<CombinationManagement.CombinationInfo[]>("getallcodeitemcomb", {});
  }
};

export { combinationManagementApi };
