<template>
  <div class="container-box">
    <NumberSource :sourceTypeID="sourceTypeID" :isCompany="true" />
  </div>
</template>

<script setup lang="ts" name="unitNumberSource">
import { ref } from "vue";
import NumberSource from "../globalNumberSource/components/numberSource.vue";
const sourceTypeID = ref("464564896820003");
</script>

<style lang="scss" scoped>
.container-box {
  width: 100%;
  height: 100%;
}
</style>
