/**
 * @description  套餐加项包接口
 */

export namespace AddPackage {
  /** 套餐加项包信息 */
  export interface AddPackageInfo {
    id: string;
    addPackageCode: string;
    addPackageName: string;
    gender: string;
    introduce: string;
    price: number;
    status: string;
    createDate: string;
    companyCode: string;
    companyName: string;
    optionalQuantity: number;
    isCompany: string;
  }
  /** 套餐加项包详情 */
  export interface AddPackageDetail {
    addPackageCode: string;
    combCode: string;
    combName: string;
  }
}
