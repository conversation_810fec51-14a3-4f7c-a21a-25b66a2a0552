<!-- 组织管理 -->
<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable ref="proTable" :columns="columns" :request-api="sysOrgApi.page" :init-param="initParam">
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <s-button suffix="医院机构" @click="onOpen(FormOptEnum.ADD)" />
          <s-button
            type="danger"
            v-auth="userButtonCode.操作机构"
            plain
            suffix="医院机构"
            :opt="FormOptEnum.DELETE"
            :disabled="!scope.isSelected"
            @click="onDelete(scope.selectedListIds, '删除所选医院机构')"
          />
        </template>
        <!-- 状态 -->
        <template #status="scope">
          <el-tag v-if="scope.row.status === CommonStatusEnum.ENABLE" type="success">{{
            dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status)
          }}</el-tag>
          <el-tag v-else type="danger">{{ dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status) }}</el-tag>
        </template>
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
            <s-button
              v-auth="userButtonCode.操作机构"
              link
              :opt="FormOptEnum.DELETE"
              @click="onDelete([scope.row.id], `删除【${scope.row.name}】机构`)"
            />
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script setup lang="ts" name="sysOrg">
import { sysOrgApi, SysOrg, userButtonCode } from "@/api";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { SysDictEnum, FormOptEnum, CommonStatusEnum } from "@/enums";
import { useHandleData } from "@/hooks/useHandleData";
import { useDictStore } from "@/stores/modules";
import Form from "./components/form.vue";

const dictStore = useDictStore(); //字典仓库
// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);

interface InitParam {
  sortField: string;
}
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<InitParam>({ sortField: "" });
// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

// 表格配置项
const columns: ColumnProps<SysOrg.SysOrgInfo>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "searchKey", label: "编码或名称", search: { el: "input" }, isShow: false },
  { prop: "orgName", label: "医院名称" },
  { prop: "orgCode", label: "医院编码" },
  { prop: "areaName", label: "院区名称" },
  { prop: "areaCode", label: "院区编码" },
  { prop: "status", label: "状态", enum: statusOptions, search: { el: "tree-select" } },
  { prop: "createDate", label: "创建时间" },
  { prop: "operation", label: "操作", width: 230, fixed: "right" }
];

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);

/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | SysOrg.SysOrgInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(sysOrgApi.delete, { ids }, msg);
  RefreshTable();
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.refresh(); //刷新表格
}
</script>

<style lang="scss" scoped></style>
