{"name": "<PERSON><PERSON><PERSON>", "private": false, "version": "1.5.0", "type": "module", "description": "基础后台管理系统", "license": "MIT", "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vue-tsc && vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@iconify/vue": "^4.1.1", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.7", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "default-passive-events": "^2.0.0", "echarts": "^5.5.0", "element-plus": "^2.5.6", "exceljs": "^4.4.0", "highlight.js": "^11.9.0", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qs": "^6.11.2", "screenfull": "^6.0.2", "secure-ls": "^2.0.0", "sortablejs": "^1.15.2", "vue": "^3.4.21", "vue-cropper": "^1.1.1", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "@iconify/json": "^2.2.187", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.12", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.17", "cz-git": "^1.8.0", "czg": "^1.8.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.22.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "naive-ui": "^2.38.1", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.71.1", "sm-crypto": "^0.3.13", "standard-version": "^9.5.0", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.0.0", "typescript": "^5.3.3", "unocss": "^0.58.5", "unplugin-auto-import": "^0.17.5", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.1", "vite-plugin-pwa": "^0.19.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}