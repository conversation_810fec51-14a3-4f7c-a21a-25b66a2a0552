<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable ref="proTable" title="加项包列表" :columns="columns" :request-api="addPackageApi.page" :pagination="false">
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <s-button suffix="加项包" @click="onOpen(FormOptEnum.ADD)" />
          <s-button
            type="danger"
            plain
            suffix="加项包"
            :opt="FormOptEnum.DELETE"
            :disabled="!scope.isSelected"
            @click="onDelete(scope.selectedListIds, '删除所选加项包')"
          />
        </template>
        <!-- 性别 -->
        <template #gender="scope">
          <div>{{ dictStore.dictTranslation(SysDictEnum.GENDER, scope.row.gender) }}</div>
        </template>
        <!-- 状态 -->
        <template #status="scope">
          <el-tag v-if="scope.row.status === CommonStatusEnum.ENABLE" type="success">{{
            dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status)
          }}</el-tag>
          <el-tag v-else type="danger">{{ dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status) }}</el-tag>
        </template>
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
            <s-button link :opt="FormOptEnum.DELETE" @click="onDelete([scope.row.id], `删除【${scope.row.addPackageName}】`)" />
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script lang="tsx" setup name="addPackage">
import { addPackageApi, AddPackage, companyManagementApi } from "@/api";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { SysDictEnum, FormOptEnum, CommonStatusEnum } from "@/enums";
import { useDictStore } from "@/stores/modules";
import { useHandleData } from "@/hooks/useHandleData";
import Form from "./components/form/index.vue";

const dictStore = useDictStore(); //字典仓库
const genderOptions = dictStore.getDictList(SysDictEnum.GENDER); //性别选项
const addPackageTypeOptions = [
  { label: "个人", value: "N" },
  { label: "团体", value: "Y" }
]; //类型选项
const companyOptions = ref<{ label: string; value: string }[]>([]);
// 表格配置项
const columns: ColumnProps<AddPackage.AddPackageInfo>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "searchKey", label: "编码/名称", search: { el: "input", defaultValue: "" }, isShow: false },
  {
    prop: "gender",
    label: "性别",
    enum: genderOptions,
    search: { el: "select", defaultValue: "" },
    isShow: false
  },
  {
    prop: "isCompany",
    label: "加项包类型",
    enum: addPackageTypeOptions,
    search: { el: "select", defaultValue: addPackageTypeOptions[0].value },
    isShow: false
  },
  {
    prop: "companyCode",
    label: "单位名称",
    search: {
      defaultValue: "",
      render: ({ searchParam }) => {
        return (
          <el-select
            vModel={searchParam.companyCode}
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入单位名称"
            remote-show-suffix
            disabled={searchParam.isCompany === "N"}
            remote-method={remoteMethod}
          >
            {companyOptions.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    },
    isShow: false
  },
  { prop: "addPackageCode", label: "加项包编码" },
  { prop: "addPackageName", label: "加项包名称" },
  { prop: "introduce", label: "加项包简介" },
  { prop: "gender", label: "适用性别" },
  { prop: "price", label: "价格" },
  { prop: "companyName", label: "单位名称" },
  { prop: "optionalQuantity", label: "可选项目数量" },
  { prop: "status", label: "状态" },
  { prop: "createDate", label: "创建时间" },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
];

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);
// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | AddPackage.AddPackageInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.refresh(); //刷新表格
  proTable.value?.clearSelection();
}

/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(addPackageApi.delete, { ids }, msg);
  RefreshTable();
}

// 单位远程搜索
async function remoteMethod(query: string) {
  if (query?.trim()) {
    // 添加可选链操作防止空值
    try {
      const { data } = await companyManagementApi.page({
        pageNum: 1,
        pageSize: 50,
        searchKey: query.trim() // 增加trim处理
      });
      companyOptions.value = data.list.map(item => ({
        label: item.companyName,
        value: item.companyCode || "" // 确保value不为undefined
      }));
    } catch (error) {
      companyOptions.value = [];
      console.error("单位搜索失败:", error);
    }
  } else {
    companyOptions.value = [];
  }
}
</script>

<style lang="scss" scoped></style>
