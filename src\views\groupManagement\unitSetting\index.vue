<template>
  <div class="unit-package-container">
    <!-- 左右布局 -->
    <el-row :gutter="24" class="main-content">
      <!-- 左侧：套餐列表 -->
      <el-col :span="16">
        <el-card class="package-list-card" shadow="never">
          <!-- 查询表单 -->
          <template #header>
            <div class="search-form">
              <el-form :model="packageSearchForm" size="small" label-width="70px" class="search-form-content">
                <el-form-item label="单位名称" class="form-item">
                  <el-select
                    v-model="packageSearchForm.unitId"
                    placeholder="请选择或搜索单位"
                    clearable
                    filterable
                    remote
                    @change="searchUnits"
                    :loading="unitLoading"
                    style="width: 100%"
                    class="custom-select"
                    size="default"
                  >
                    <template #prefix>
                      <el-icon class="input-icon"><OfficeBuilding /></el-icon>
                    </template>
                    <el-option v-for="unit in unitOptions" :key="unit.companyCode" :label="unit.companyName" :value="unit.companyCode" />
                  </el-select>
                </el-form-item>

                <el-form-item label="体检次数" class="form-item">
                  <el-select
                    v-model="packageSearchForm.checkupTimes"
                    placeholder="请选择体检次数"
                    clearable
                    style="width: 100%"
                    class="custom-select"
                    size="default"
                  >
                    <template #prefix>
                      <el-icon class="input-icon"><Calendar /></el-icon>
                    </template>

                    <el-option
                      v-for="item in unitTimes"
                      :key="item.companyTimes"
                      :label="`第${item.companyTimes}次体检`"
                      :value="item.companyTimes"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </template>

          <!-- 单位信息表单 -->
          <el-form :model="unitInfo" label-width="auto" style=" display: grid;grid-template-columns: 1fr 1fr;gap: 20px; width: 100%;">
            <el-form-item label="页面标题">
              <el-input v-model="unitInfo.title" />
            </el-form-item>
            <el-form-item label="体检有效期">
              <el-input v-model="unitInfo.validityPeriod" />
            </el-form-item>

            <el-form-item label="上传封面">
              <div class="cover-upload-container">
                <el-upload
                  ref="uploadRef"
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  :limit="1"
                  :on-exceed="handleExceed"
                  :on-change="handleFileChange"
                  :before-upload="beforeUpload"
                  :file-list="fileList"
                  accept="image/*"
                  class="cover-upload"
                >
                  <template #default>
                    <div class="upload-trigger">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">上传封面</div>
                      <div class="upload-hint">只能上传一张图片</div>
                    </div>
                  </template>

                  <template #file="{ file }">
                    <div class="upload-file-item">
                      <img class="upload-image" :src="file.url" alt="封面图片" />
                      <div class="upload-overlay">
                        <div class="upload-actions">
                          <el-button
                            type="primary"
                            :icon="ZoomIn"
                            circle
                            size="small"
                            @click="handlePreview(file)"
                          />
                          <el-button
                            type="danger"
                            :icon="Delete"
                            circle
                            size="small"
                            @click="handleRemove(file)"
                          />
                        </div>
                      </div>
                      <div class="file-info">
                        <div class="file-size">{{ formatfileSize(file.size || 0) }}</div>
                      </div>
                    </div>
                  </template>
                </el-upload>

                <!-- 文件信息显示 -->
                <div v-if="unitInfo.imgData" class="file-details">
                  <el-descriptions :column="2" size="small" border>
                    <el-descriptions-item label="文件大小">
                      {{ formatfileSize(unitInfo.fileSize) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="数据状态">
                      {{ unitInfo.imgData ? '已转换为二进制' : '未上传' }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="体检须知">
             <WangEdit v-model="unitInfo.notes" height="300px" ref="wangeditor" />
              <!-- <el-input v-model="unitInfo.notes" type="textarea" style="width: 90%" :autosize="{ minRows: 8, maxRows: 12 }" /> -->
            </el-form-item>

            <el-dialog v-model="dialogVisible">
              <img w-full :src="dialogImageUrl" alt="Preview Image" />
            </el-dialog>
          </el-form>

          <template #footer><el-button>重置</el-button> <el-button type="primary" @click="saveUnitInfo"> 保存 </el-button></template>
        </el-card>
      </el-col>

      <!-- 右侧：套餐详情 -->
      <el-col :span="8">
        <el-card class="package-list-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>客户端预约界面</span>
            </div>
          </template>
          <div class="phone-context">
            <Phone :phoneStatus="true" :unitInfo="unitInfo" phone-title="体检单位登录页" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import Phone from "@/components/Phone/index.vue";
import WangEdit from "@/components/WangEdit/index.vue";
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, UploadFile, UploadFiles, UploadInstance, genFileId } from "element-plus";
import { Search, Refresh, OfficeBuilding, Calendar, Document, Plus, ZoomIn, Delete } from "@element-plus/icons-vue";
import type { UnitPackage, PackageItem, SearchForm, Pagination, CompanyTimesUI } from "./interface";
import { companyManagementApi } from "@/api/modules";


const unitInfo:UnitInfo = reactive({
  title: "",
  companyName:"",
  validityPeriod:"",
  notes:"" ,
  imgData:"",
  fileSize:0
});

interface UnitInfo {
  title: string;
  companyName: string;
  validityPeriod: string;
  notes: string;
  imgData: string;
  fileSize: number;
}

provide('unitInfoFromUnitSetting', unitInfo)

// unitSettingStore.unitInfo = unitInfo


function saveUnitInfo() {
  console.log("unitInfo", unitInfo);
}

// 套餐查询表单
const packageSearchForm = ref({
  unitId: "",
  checkupTimes: null,
  packageName: ""
});

// 分页信息
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 表格数据
const packageList = ref<any[]>([]);

// 单位选择相关
const unitOptions = ref<any[]>([]);
const unitLoading = ref(false);

// 初始化
onMounted(async () => {
  await fetchUnits();
  await fetchCompanyTimes(packageSearchForm.value.unitId);

  //await fetchAvailableItems();
});

//单位体检次数
const unitTimes = ref<CompanyTimesUI[]>([]);

// 获取单位列表
const fetchUnits = async () => {
  const { data } = await companyManagementApi.page({
    pageNum: pagination.currentPage,
    pageSize: pagination.pageSize,
    searchKey: packageSearchForm.value.unitId
  });

  unitOptions.value = data.list;
  const [firstCompany] = unitOptions.value;
  packageSearchForm.value.unitId = firstCompany?.companyCode;
  unitInfo.companyName = firstCompany?.companyName;
};

//获取体检次数
async function fetchCompanyTimes(companyCode: string) {
  const response = await companyManagementApi.queryCompanyTimes(companyCode);

  console.log("response", response);
  const { data } = response;

  const [fristTimes] = data;
  packageSearchForm.value.checkupTimes = fristTimes?.companyTimes;
  unitTimes.value = data;
}

// 搜索选择单位 获取单位体检次数和 单位套餐
const searchUnits = async () => {
  await fetchCompanyTimes(packageSearchForm.value.unitId);
};

// 这些函数已被新的套餐查询函数替代

// 上传相关
const uploadRef = ref<UploadInstance>();
const fileList = ref<UploadFiles>([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);

// 文件大小格式化
const formatfileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 文件转换为二进制
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // 移除 data:image/jpeg;base64, 前缀，只保留二进制数据
      const base64Data = result.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = error => reject(error);
  });
};

// 上传前验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 文件变化处理
const handleFileChange = async (file: UploadFile, fileList: UploadFiles) => {
  if (file.status === 'ready' && file.raw) {
    try {
      // 转换为二进制数据
      const binaryData = await fileToBase64(file.raw);

      // 更新unitInfo
      unitInfo.imgData = binaryData;
      unitInfo.fileSize = file.raw.size;

      ElMessage.success(`封面上传成功！文件大小：${formatfileSize(file.raw.size)}`);

      console.log('文件信息:', {
        name: file.name,
        size: file.raw.size,
        type: file.raw.type,
        binaryDataLength: binaryData.length
      });
    } catch (error) {
      console.error('文件处理失败:', error);
      ElMessage.error('文件处理失败，请重试');
    }
  }
};

// 超出文件数量限制
const handleExceed = (files: File[]) => {
  uploadRef.value!.clearFiles();
  const file = files[0];
  // 将 File 转换为 UploadRawFile
  const uploadRawFile = file as any;
  uploadRawFile.uid = genFileId();
  uploadRef.value!.handleStart(uploadRawFile);
  ElMessage.warning('只能上传一张封面图片，已自动替换');
};

// 移除文件
const handleRemove = (_file: UploadFile) => {
  // 清空数据
  unitInfo.imgData = "";
  unitInfo.fileSize = 0;

  // 清空文件列表
  fileList.value = [];

  ElMessage.info('封面已移除');
};

// 预览图片
const handlePreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url!;
  dialogVisible.value = true;
};
</script>

<style scoped lang="scss">
.unit-package-container {
  .search-card {
    margin-bottom: 20px;
  }
  .main-content {
    height: 85vh;
  }
  .package-list-card {
    height: 100%;
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }
    .search-form {
      margin-bottom: 15px;
      overflow: hidden;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgb(102 126 234 / 15%);
      .search-form-header {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: rgb(255 255 255 / 10%);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgb(255 255 255 / 10%);
        .search-icon {
          margin-right: 8px;
          font-size: 18px;
          color: #ffffff;
        }
        .search-title {
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          letter-spacing: 0.5px;
        }
      }
      .search-form-content {
        padding: 5px;
        background: rgb(255 255 255 / 95%);
        backdrop-filter: blur(10px);
        :deep(.el-form-item) {
          margin-bottom: 16px;
          &.form-buttons {
            margin-top: 8px;
            margin-bottom: 0;
            text-align: center;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
        :deep(.el-form-item__label) {
          font-size: 13px;
          font-weight: 500;
          line-height: 32px;
          color: #606266;
        }
        :deep(.custom-select) {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
            }
            &.is-focus {
              box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
            }
          }
          .input-icon {
            font-size: 14px;
            color: #667eea;
          }
        }
        :deep(.custom-input) {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
            }
            &.is-focus {
              box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
            }
          }
          .input-icon {
            font-size: 14px;
            color: #667eea;
          }
        }
        .search-btn {
          padding: 8px 20px;
          margin-right: 12px;
          font-weight: 500;
          border: none;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
          transition: all 0.3s ease;
          &:hover {
            box-shadow: 0 6px 16px rgb(102 126 234 / 40%);
            transform: translateY(-2px);
          }
          &:active {
            transform: translateY(0);
          }
        }
        .reset-btn {
          padding: 8px 20px;
          font-weight: 500;
          color: #606266;
          background: #ffffff;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          transition: all 0.3s ease;
          &:hover {
            background: #f5f7fa;
            border-color: #c0c4cc;
            box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
            transform: translateY(-1px);
          }
          &:active {
            transform: translateY(0);
          }
        }
      }
    }
    .pagination-container {
      display: flex;
      justify-content: center;
      padding: 10px 0;
      margin-top: 15px;
      border-top: 1px solid #e4e7ed;
    }
    :deep(.el-table) {
      .el-table__row {
        cursor: pointer;
        &:hover {
          background-color: #f5f7fa;
        }
      }
      .current-row {
        background-color: #ecf5ff !important;
        &:hover {
          background-color: #ecf5ff !important;
        }
      }
    }
  }
  .package-detail-card {
    height: 100%;
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
    .package-info {
      margin-bottom: 20px;
    }
    .package-items-detail {
      .detail-tabs {
        :deep(.el-tabs__content) {
          padding: 15px 0;
        }
      }
    }
    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow-y: auto;
    }
  }
  .empty-card {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    :deep(.el-card__body) {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}
.phone-context {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 上传组件样式
.cover-upload-container {
  .cover-upload {
    :deep(.el-upload) {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      transition: all 0.3s ease;
      &:hover {
        background-color: #f5f7fa;
        border-color: #409eff;
      }
    }
    :deep(.el-upload-list__item) {
      overflow: hidden;
      border-radius: 8px;
    }
  }
  .upload-trigger {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 148px;
    height: 148px;
    .upload-icon {
      margin-bottom: 8px;
      font-size: 28px;
      color: #8c939d;
    }
    .upload-text {
      margin-bottom: 4px;
      font-size: 14px;
      color: #606266;
    }
    .upload-hint {
      font-size: 12px;
      color: #909399;
    }
  }
  .upload-file-item {
    position: relative;
    width: 148px;
    height: 148px;
    overflow: hidden;
    border-radius: 8px;
    .upload-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .upload-overlay {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgb(0 0 0 / 50%);
      opacity: 0;
      transition: opacity 0.3s ease;
      &:hover {
        opacity: 1;
      }
      .upload-actions {
        display: flex;
        gap: 8px;
      }
    }
    .file-info {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 8px;
      background: linear-gradient(transparent, rgb(0 0 0 / 70%));
      .file-size {
        font-size: 12px;
        color: white;
        text-align: center;
      }
    }
  }
  .file-details {
    margin-top: 16px;
    :deep(.el-descriptions) {
      .el-descriptions__label {
        width: 80px;
        font-weight: 500;
        color: #606266;
      }
      .el-descriptions__content {
        color: #303133;
      }
    }
  }
}

// 全局样式调整
:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        font-weight: bold;
        color: #303133;
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
