<template>
  <div>
    <form-container v-model="visible" :title="`${timeSlotsProps.opt}时段`" form-size="500px" @close="onClose">
      <el-form
        class="form"
        ref="timeSlotsFormRef"
        :rules="rules"
        :disabled="timeSlotsProps.disabled"
        :model="timeSlotsProps.record"
        :hide-required-asterisk="timeSlotsProps.disabled"
        label-width="auto"
        label-suffix=" :"
      >
        <s-form-item label="时段名称" prop="timeSlotName">
          <s-input v-model="timeSlotsProps.record.timeSlotName" placeholder="请输入时段名称"></s-input>
        </s-form-item>
        <s-form-item label="开始时间" prop="startTime">
          <el-time-picker v-model="timeSlotsProps.record.startTime" placeholder="开始时间" style="width: 100%" format="HH:mm" value-format="HH:mm" />
        </s-form-item>
        <s-form-item label="结束时间" prop="endTime">
          <el-time-picker v-model="timeSlotsProps.record.endTime" placeholder="结束时间" style="width: 100%" format="HH:mm" value-format="HH:mm" />
        </s-form-item>
        <s-form-item label="时段范围" prop="timePeriod">
          <s-select v-model="timeSlotsProps.record.timePeriod" placeholder="请选择时段范围" :options="timePeriodOptions"></s-select>
        </s-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!timeSlotsProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { TimeSlots, timeSlotsApi } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";

const dictStore = useDictStore(); //字典仓库
const visible = ref(false); //是否显示表单
// 时段范围选项
const timePeriodOptions = dictStore.getDictList(SysDictEnum.TIME_INTERVAL);

// 表单参数
const timeSlotsProps = reactive<FormProps.Base<TimeSlots.TimeSlotsInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  timeSlotName: [required("请输入时段名称")],
  startTime: [required("请选择开始时间")],
  endTime: [required("请选择结束时间")],
  timePeriod: [required("请选择时段范围")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<TimeSlots.TimeSlotsInfo>) {
  Object.assign(timeSlotsProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    timeSlotsProps.record = {
      id: "",
      timeSlotName: "",
      startTime: "",
      endTime: "",
      timePeriod: ""
    };
  }
  visible.value = true; //显示表单
  if (props.opt == FormOptEnum.EDIT) {
    timeSlotsProps.record = JSON.parse(JSON.stringify(props.record));
  }
}

// 提交数据（新增/编辑）
const timeSlotsFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  timeSlotsFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    await timeSlotsApi
      .submitForm(timeSlotsProps.record, timeSlotsProps.opt === FormOptEnum.EDIT)
      .then(() => {
        ElMessage.success("保存成功");
        timeSlotsProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container {
  .form {
    margin-top: 20px;
  }
  :deep(.el-dialog__body) {
    margin-top: 0;
  }
  :deep(.el-drawer__body) {
    margin-top: -20px;
  }
}
</style>
