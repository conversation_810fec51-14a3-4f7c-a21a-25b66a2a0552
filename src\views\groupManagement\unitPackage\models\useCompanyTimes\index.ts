/*
 * @Author: Reaper
 * @Date: 2025-06-13 18:30:44
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-14 14:35:45
 * @Description: 请填写简介
 */
import { companyManagementApi } from "@/api";
import { usePackageQuery } from "../usePackageQuery";

const { packageQuery } = usePackageQuery();

interface CompanyTimes {
  companyCode: string;
  companyTimes: number;
  beginDate: Date;
  endDate: Date;
  onLineEndDate: Date;
}

export const useCompanyTimes = () => {
  const companyTimes = ref([]);

  async function getCompanyTimes(companyCode: string) {
    const { data } = await companyManagementApi.queryCompanyTimes(companyCode);
    companyTimes.value = data;
    return data;
  }

  async function init(companyCode: string) {
    const { data } = await companyManagementApi.queryCompanyTimes(companyCode);
    companyTimes.value = data;
    packageQuery.value.companyTimes = data[0]?.companyTimes ?? null;
    return data;
  }

  return {
    companyTimes,
    getCompanyTimes,
    init
  };
};
