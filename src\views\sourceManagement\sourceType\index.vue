<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable
        ref="proTable"
        title="号源类型列表"
        :columns="columns"
        :data="tableData"
        @search="onSearch"
        @reset="onSearch"
        :pagination="false"
        :toolButton="['setting', 'search']"
      >
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <el-button type="primary" link @click="onOpen(FormOptEnum.EDIT, scope.row)">关联时段</el-button>
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script lang="ts" setup name="sourceType">
import { sourceTypeApi, SourceType } from "@/api";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { FormOptEnum } from "@/enums";
import Form from "./components/form/index.vue";

// 表格配置项
const columns: ColumnProps<SourceType.SourceTypeInfo>[] = [
  { prop: "searchKey", label: "类型名称", search: { el: "input" }, isShow: false },
  { prop: "sourceTypeName", label: "类型名称" },
  { prop: "operation", label: "操作", width: 230, fixed: "right" }
];
// 表格数据
const tableData = ref<SourceType.SourceTypeInfo[]>([]);
// 过滤后的数据
const filterTable = ref<SourceType.SourceTypeInfo[]>([]);
// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);
// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

/**
 * 搜索
 */
async function onSearch() {
  const { searchKey } = proTable.value?.searchParam || {};
  tableData.value = filterTable.value.filter(item => {
    // 名称/编码搜索
    const keywordMatch = searchKey ? [item.sourceTypeName].some(field => field?.toLowerCase().includes(searchKey.toLowerCase())) : true;

    return keywordMatch;
  });
}
/**
 * 获取时段列表
 */
async function getPage() {
  const { data } = await sourceTypeApi.page();
  tableData.value = data;
  filterTable.value = data;
}
/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | SourceType.SourceTypeInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.clearSelection();
  getPage();
}

onMounted(() => {
  getPage();
});
</script>

<style lang="scss" scoped></style>
