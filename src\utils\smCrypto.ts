/**
 * @description smCrypto 加密解密工具
 */

import smCrypto from "sm-crypto";
const { sm2 } = smCrypto;
const cipherMode = 0; // 1 - C1C3C2，0 - C1C2C3，默认为1
const publicKey =
  "04781E6715FF979D66139BA4C38026738224F66B632E2ED2C03250C4AB9F7D33E0DC3858CEE1F54256464AE3288A5AA0725764FB35B0E124617B42814DAE601A86";

/**
 * 国密加解密工具类
 */
export default {
  // SM2加密
  doSm2Encrypt(msgString: string) {
    return sm2.doEncrypt(msgString, publicKey, cipherMode);
  }
};
