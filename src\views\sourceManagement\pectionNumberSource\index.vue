<template>
  <div class="container-box">
    <NumberSource :sourceTypeID="sourceTypeID" />
  </div>
</template>

<script setup lang="ts" name="pectionNumberSource">
import { ref } from "vue";
import NumberSource from "../globalNumberSource/components/numberSource.vue";
const sourceTypeID = ref("464564896820005");
</script>

<style lang="scss" scoped>
.container-box {
  width: 100%;
  height: 100%;
}
</style>
