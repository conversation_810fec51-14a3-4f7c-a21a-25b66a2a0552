<template>
  <div class="add-item-template-container">
    <!-- 左右布局 -->
    <el-row :gutter="24" class="main-content">
      <!-- 左侧：加项模板列表 -->
      <el-col :span="6">
        <el-card class="template-list-card" shadow="never">
          <!-- 新增按钮 -->
          <div class="add-button-section">
            <el-button type="primary" @click="createNewTemplate" :icon="Plus" style="width: 100%">
              新增模板
            </el-button>
          </div>

          <!-- 查询表单 -->
          <div class="search-form">
            <el-form :model="searchForm" size="small" label-width="70px" class="search-form-content">
              <el-form-item label="模板名称" class="form-item">
                <el-input v-model="searchForm.templateName" placeholder="请输入模板名称关键词" clearable style="width: 100%"
                  class="custom-input" size="default">
                  <template #prefix>
                    <el-icon class="input-icon">
                      <Document />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>

          <!-- 加项模板表格 -->
          <el-table :data="templateList" border stripe v-loading="loading" :header-cell-style="{
            background: '#f5f7fa',
            color: '#303133',
            fontWeight: 'bold'
          }" @row-click="handleRowClick" :highlight-current-row="true" height="63vh">
            <el-table-column prop="templateName" label="模板名称" show-overflow-tooltip />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50]" layout="total, sizes, prev, pager, next" :total="pagination.total" background
              small @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：模板详情和项目设置 -->
      <el-col :span="18">
        <el-card class="template-detail-card" shadow="never" v-if="selectedTemplate">
          <template #header>
            <div class="card-header">
              <span class="title">模板项目设置 - {{ selectedTemplate.templateName }}</span>
              <div class="header-actions">
                <el-button size="small" @click="saveTemplateName" :disabled="!isEditingName">保存名称</el-button>
                <el-button size="small" @click="editTemplateName" v-if="!isEditingName">编辑名称</el-button>
                <el-button size="small" @click="cancelEditName" v-if="isEditingName">取消</el-button>
              </div>
            </div>
          </template>

          <div class="template-items-container">
            <!-- 模板名称编辑 -->
            <div class="template-name-section" v-if="isEditingName">
              <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="80px">
                <el-form-item label="模板名称" prop="templateName">
                  <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit
                    style="width: 300px" />
                </el-form-item>
              </el-form>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="search-section">
              <el-input v-model="itemSearchKeyword" placeholder="请输入项目名称" style="width: 500px">
                <template #prepend>
                  <el-select v-model="selectedCategory" placeholder="" style="width: 200px" @change="onSearchItems">
                    <el-option :label="item.label" :value="item.value" v-for="(item, index) in categoryOptions"
                      :key="index" />
                  </el-select>
                </template>
                <template #append>
                  <el-button :icon="Search" @click="onSearchItems" />
                </template>
              </el-input>
              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button type="primary" @click="saveTemplateItems" :disabled="selectedItems.length === 0">
                  保存模板项目
                </el-button>
                <el-button @click="selectedItems = []">清空选择</el-button>
              </div>
            </div>

            <!-- 体检项目选择区域 -->
            <div class="items-selection-container">
              <div class="items-list-section">
                <div class="section-title">可选体检项目</div>
                <div class="items-container">
                  <div v-for="(group, index) in filteredItems" :key="index" class="item-group">
                    <div class="item-group-title">
                      <el-check-tag checked>{{ group.department }}</el-check-tag>
                    </div>
                    <el-checkbox-group v-model="selectedItemIds" class="item-checkbox-group">
                      <el-checkbox v-for="item in group.items" :key="item.id" :label="item.id" border
                        class="item-checkbox">
                        {{ `${item.itemName}　　￥${item.itemPrice}` }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <el-empty v-if="filteredItems.length === 0" description="暂无数据" />
                </div>
              </div>

              <div class="selected-items-section">
                <div class="selected-header">
                  <span class="selected-title">已选择项目</span>
                  <span class="selected-count">共 {{ selectedItems.length }} 项</span>
                  <span class="selected-price">总价：{{ totalPrice.toFixed(2) }}元</span>
                </div>
                <el-table :data="selectedItems" style="width: 100%; overflow-y: hidden;" height="63vh" max-height="63vh"
                  border>
                  <el-table-column prop="itemCode" label="项目编码" width="120" />
                  <el-table-column prop="itemName" label="项目名称" />
                  <el-table-column prop="department" label="科室" width="100" />
                  <el-table-column prop="itemPrice" label="价格" width="100" />
                  <el-table-column fixed="right" label="操作" width="110">
                    <template #default="scope">
                      <el-button link type="danger" @click="removeSelectedItem(scope.row)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 未选择模板时的提示 -->
        <el-card class="empty-card" shadow="never" v-else>
          <el-empty description="请选择左侧模板或点击新增按钮创建模板" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts" name="addItemTemplate">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { Search, Refresh, Document, Plus } from '@element-plus/icons-vue';
import type { AddItemTemplate, TemplateItem, SearchForm, Pagination, TemplateForm } from './interface';

// 搜索表单
const searchForm = reactive<SearchForm>({
  templateName: '',
  templateCode: ''
});

// 分页信息
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 表格数据
const templateList = ref<AddItemTemplate[]>([]);
const loading = ref(false);

// 选中的模板
const selectedTemplate = ref<AddItemTemplate | null>(null);

// 模板表单
const templateForm = reactive<TemplateForm>({
  id: '',
  templateName: '',
  description: ''
});

const templateFormRef = ref<FormInstance>();

// 表单验证规则
const templateRules = reactive<FormRules>({
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在2到50个字符', trigger: 'blur' }
  ]
});

// 编辑状态
const isEditingName = ref(false);

// 项目相关
const availableItems = ref<TemplateItem[]>([]);
const selectedItems = ref<TemplateItem[]>([]);
const selectedItemIds = ref<string[]>([]);
const itemSearchKeyword = ref('');
const selectedCategory = ref('-1');

// 科室分类选项
const categoryOptions = computed(() => {
  const departments = [...new Set(availableItems.value.map(item => item.department))];
  return [
    { value: '-1', label: '全部科室' },
    ...departments.map(dept => ({ value: dept, label: dept }))
  ];
});

// 过滤后的项目列表（按科室分组）
const filteredItems = computed(() => {
  let result = availableItems.value;

  // 按科室过滤
  if (selectedCategory.value !== '-1') {
    result = result.filter(item => item.department === selectedCategory.value);
  }

  // 按关键字搜索
  if (itemSearchKeyword.value) {
    const keyword = itemSearchKeyword.value.toLowerCase();
    result = result.filter(item =>
      item.itemName.toLowerCase().includes(keyword) ||
      item.itemCode.toLowerCase().includes(keyword)
    );
  }

  // 按科室分组
  const groupedItems = result.reduce((groups, item) => {
    const department = item.department;
    if (!groups[department]) {
      groups[department] = [];
    }
    groups[department].push(item);
    return groups;
  }, {} as Record<string, TemplateItem[]>);

  // 转换为数组格式，每个元素包含科室名称和项目列表
  return Object.entries(groupedItems).map(([department, items]) => ({
    department,
    items
  }));
});

// 计算总价格
const totalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => total + item.itemPrice, 0);
});

// 监听选中项目变化
watch(selectedItemIds, (newIds) => {
  selectedItems.value = availableItems.value.filter(item => newIds.includes(item.id));
});

// 初始化
onMounted(() => {
  fetchTemplateList();
  fetchAvailableItems();
});

// 获取模板列表
const fetchTemplateList = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    setTimeout(() => {
      // 真实的加项模板数据
      const mockData = [
        {
          id: "1",
          templateCode: "TPL001",
          templateName: "基础体检模版",
          itemCount: 5,
          totalPrice: 280,
          status: 1,
          createTime: '2024-01-15 10:30:00',
          description: '包含基础体检常用加项项目'
        },
        {
          id: "2",
          templateCode: "TPL002",
          templateName: "心脑血管专项模版",
          itemCount: 8,
          totalPrice: 450,
          status: 1,
          createTime: '2024-01-20 14:20:00',
          description: '心脑血管疾病筛查专项模版'
        },
        {
          id: "3",
          templateCode: "TPL003",
          templateName: "肿瘤筛查模版",
          itemCount: 6,
          totalPrice: 380,
          status: 1,
          createTime: '2024-02-01 09:15:00',
          description: '常见肿瘤标志物筛查加项'
        },
        {
          id: "4",
          templateCode: "TPL004",
          templateName: "妇科专项模版",
          itemCount: 4,
          totalPrice: 220,
          status: 1,
          createTime: '2024-02-10 16:45:00',
          description: '妇科疾病筛查专项加项'
        },
        {
          id: "5",
          templateCode: "TPL005",
          templateName: "老年体检模版",
          itemCount: 7,
          totalPrice: 320,
          status: 1,
          createTime: '2024-02-15 11:30:00',
          description: '老年人群专项体检加项'
        },
        {
          id: "6",
          templateCode: "TPL006",
          templateName: "入职体检模版",
          itemCount: 3,
          totalPrice: 150,
          status: 1,
          createTime: '2024-02-20 13:20:00',
          description: '入职体检常用加项项目'
        },
        {
          id: "7",
          templateCode: "TPL007",
          templateName: "糖尿病筛查模版",
          itemCount: 5,
          totalPrice: 180,
          status: 1,
          createTime: '2024-03-01 10:00:00',
          description: '糖尿病相关指标筛查加项'
        },
        {
          id: "8",
          templateCode: "TPL008",
          templateName: "肝功能专项模版",
          itemCount: 6,
          totalPrice: 200,
          status: 1,
          createTime: '2024-03-05 15:30:00',
          description: '肝功能全面检查加项'
        },
        {
          id: "9",
          templateCode: "TPL009",
          templateName: "肾功能专项模版",
          itemCount: 4,
          totalPrice: 160,
          status: 1,
          createTime: '2024-03-10 12:15:00',
          description: '肾功能检查专项加项'
        },
        {
          id: "10",
          templateCode: "TPL010",
          templateName: "甲状腺功能模版",
          itemCount: 3,
          totalPrice: 120,
          status: 1,
          createTime: '2024-03-15 14:45:00',
          description: '甲状腺功能检查加项'
        },
        {
          id: "11",
          templateCode: "TPL011",
          templateName: "高血压筛查模版",
          itemCount: 4,
          totalPrice: 140,
          status: 1,
          createTime: '2024-03-20 09:30:00',
          description: '高血压相关指标筛查加项'
        },
        {
          id: "12",
          templateCode: "TPL012",
          templateName: "骨密度专项模版",
          itemCount: 2,
          totalPrice: 115,
          status: 1,
          createTime: '2024-03-25 16:20:00',
          description: '骨质疏松筛查专项加项'
        },
        {
          id: "13",
          templateCode: "TPL013",
          templateName: "呼吸系统模版",
          itemCount: 3,
          totalPrice: 165,
          status: 1,
          createTime: '2024-04-01 11:45:00',
          description: '呼吸系统疾病筛查加项'
        },
        {
          id: "14",
          templateCode: "TPL014",
          templateName: "消化系统模版",
          itemCount: 5,
          totalPrice: 195,
          status: 1,
          createTime: '2024-04-05 14:15:00',
          description: '消化系统疾病筛查加项'
        },
        {
          id: "15",
          templateCode: "TPL015",
          templateName: "免疫系统模版",
          itemCount: 4,
          totalPrice: 180,
          status: 1,
          createTime: '2024-04-10 10:30:00',
          description: '免疫系统功能检查加项'
        },
        {
          id: "16",
          templateCode: "TPL016",
          templateName: "营养状况模版",
          itemCount: 3,
          totalPrice: 130,
          status: 1,
          createTime: '2024-04-15 13:20:00',
          description: '营养状况评估加项'
        },
        {
          id: "17",
          templateCode: "TPL017",
          templateName: "职业病筛查模版",
          itemCount: 6,
          totalPrice: 240,
          status: 1,
          createTime: '2024-04-20 15:40:00',
          description: '职业病相关检查加项'
        },
        {
          id: "18",
          templateCode: "TPL018",
          templateName: "运动医学模版",
          itemCount: 4,
          totalPrice: 200,
          status: 1,
          createTime: '2024-04-25 12:10:00',
          description: '运动医学相关检查加项'
        },
        {
          id: "19",
          templateCode: "TPL019",
          templateName: "心理健康模版",
          itemCount: 2,
          totalPrice: 80,
          status: 1,
          createTime: '2024-05-01 09:50:00',
          description: '心理健康评估加项'
        },
        {
          id: "20",
          templateCode: "TPL020",
          templateName: "睡眠质量模版",
          itemCount: 3,
          totalPrice: 120,
          status: 1,
          createTime: '2024-05-05 16:30:00',
          description: '睡眠质量评估加项'
        }
      ];

      // 应用搜索过滤
      let filteredData = mockData;
      if (searchForm.templateName) {
        filteredData = mockData.filter(item =>
          item.templateName.includes(searchForm.templateName)
        );
      }

      const start = (pagination.currentPage - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      templateList.value = filteredData.slice(start, end);
      pagination.total = filteredData.length;
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取模板列表失败');
    loading.value = false;
  }
};

// 获取可用体检项目
const fetchAvailableItems = async () => {
  try {
    // 真实的体检项目数据
    availableItems.value = [
      // 实验室检查
      { id: "1", itemCode: "LAB001", itemName: "血常规", itemPrice: 25, department: "实验室检查", description: "血液常规检查，包括红细胞、白细胞、血小板计数等" },
      { id: "2", itemCode: "LAB002", itemName: "尿常规", itemPrice: 20, department: "实验室检查", description: "尿液常规检查，包括蛋白质、糖、酮体等" },
      { id: "3", itemCode: "LAB003", itemName: "肝功能", itemPrice: 35, department: "实验室检查", description: "肝功能检查，包括ALT、AST、总胆红素等" },
      { id: "4", itemCode: "LAB004", itemName: "肾功能", itemPrice: 30, department: "实验室检查", description: "肾功能检查，包括肌酐、尿素氮等" },
      { id: "5", itemCode: "LAB005", itemName: "血糖", itemPrice: 15, department: "实验室检查", description: "空腹血糖检查" },
      { id: "6", itemCode: "LAB006", itemName: "血脂四项", itemPrice: 40, department: "实验室检查", description: "总胆固醇、甘油三酯、高密度脂蛋白、低密度脂蛋白" },
      { id: "7", itemCode: "LAB007", itemName: "甲状腺功能", itemPrice: 45, department: "实验室检查", description: "TSH、T3、T4检查" },
      { id: "8", itemCode: "LAB008", itemName: "肿瘤标志物", itemPrice: 60, department: "实验室检查", description: "AFP、CEA、CA199等肿瘤标志物检查" },
      { id: "9", itemCode: "LAB009", itemName: "乙肝五项", itemPrice: 35, department: "实验室检查", description: "乙肝病毒标志物检查" },
      { id: "10", itemCode: "LAB010", itemName: "梅毒抗体", itemPrice: 25, department: "实验室检查", description: "梅毒螺旋体抗体检查" },
      { id: "41", itemCode: "LAB011", itemName: "糖化血红蛋白", itemPrice: 30, department: "实验室检查", description: "糖化血红蛋白检查，反映血糖控制情况" },
      { id: "42", itemCode: "LAB012", itemName: "胰岛素释放试验", itemPrice: 80, department: "实验室检查", description: "胰岛素释放功能检查" },
      { id: "43", itemCode: "LAB013", itemName: "C肽释放试验", itemPrice: 70, department: "实验室检查", description: "C肽释放功能检查" },
      { id: "44", itemCode: "LAB014", itemName: "同型半胱氨酸", itemPrice: 35, department: "实验室检查", description: "同型半胱氨酸检查，心血管疾病风险指标" },
      { id: "45", itemCode: "LAB015", itemName: "超敏C反应蛋白", itemPrice: 25, department: "实验室检查", description: "超敏C反应蛋白检查，炎症指标" },
      { id: "46", itemCode: "LAB016", itemName: "肌钙蛋白I", itemPrice: 50, department: "实验室检查", description: "肌钙蛋白I检查，心肌损伤标志物" },
      { id: "47", itemCode: "LAB017", itemName: "脑钠肽", itemPrice: 60, department: "实验室检查", description: "脑钠肽检查，心力衰竭标志物" },
      { id: "48", itemCode: "LAB018", itemName: "D-二聚体", itemPrice: 40, department: "实验室检查", description: "D-二聚体检查，血栓形成标志物" },
      { id: "49", itemCode: "LAB019", itemName: "维生素D", itemPrice: 45, department: "实验室检查", description: "维生素D检查，骨代谢指标" },
      { id: "50", itemCode: "LAB020", itemName: "微量元素", itemPrice: 70, department: "实验室检查", description: "微量元素检查，包括铁、锌、铜、镁等" },

      // 影像学检查
      { id: "11", itemCode: "IMG001", itemName: "胸部X线", itemPrice: 50, department: "影像学检查", description: "胸部正侧位X线检查" },
      { id: "12", itemCode: "IMG002", itemName: "腹部B超", itemPrice: 80, department: "影像学检查", description: "腹部脏器B超检查" },
      { id: "13", itemCode: "IMG003", itemName: "心脏彩超", itemPrice: 120, department: "影像学检查", description: "心脏彩色多普勒超声检查" },
      { id: "14", itemCode: "IMG004", itemName: "颈部B超", itemPrice: 60, department: "影像学检查", description: "颈部血管B超检查" },
      { id: "15", itemCode: "IMG005", itemName: "乳腺B超", itemPrice: 70, department: "影像学检查", description: "乳腺B超检查" },
      { id: "16", itemCode: "IMG006", itemName: "甲状腺B超", itemPrice: 65, department: "影像学检查", description: "甲状腺B超检查" },
      { id: "17", itemCode: "IMG007", itemName: "腰椎X线", itemPrice: 55, department: "影像学检查", description: "腰椎正侧位X线检查" },
      { id: "18", itemCode: "IMG008", itemName: "颈椎X线", itemPrice: 55, department: "影像学检查", description: "颈椎正侧位X线检查" },
      { id: "51", itemCode: "IMG009", itemName: "头颅CT", itemPrice: 200, department: "影像学检查", description: "头颅CT检查" },
      { id: "52", itemCode: "IMG010", itemName: "胸部CT", itemPrice: 250, department: "影像学检查", description: "胸部CT检查" },
      { id: "53", itemCode: "IMG011", itemName: "腹部CT", itemPrice: 300, department: "影像学检查", description: "腹部CT检查" },
      { id: "54", itemCode: "IMG012", itemName: "头颅MRI", itemPrice: 400, department: "影像学检查", description: "头颅MRI检查" },
      { id: "55", itemCode: "IMG013", itemName: "颈椎MRI", itemPrice: 350, department: "影像学检查", description: "颈椎MRI检查" },
      { id: "56", itemCode: "IMG014", itemName: "腰椎MRI", itemPrice: 350, department: "影像学检查", description: "腰椎MRI检查" },
      { id: "57", itemCode: "IMG015", itemName: "骨密度", itemPrice: 90, department: "影像学检查", description: "骨密度检查" },

      // 内科检查
      { id: "19", itemCode: "INT001", itemName: "内科检查", itemPrice: 30, department: "内科检查", description: "内科常规体格检查" },
      { id: "20", itemCode: "INT002", itemName: "血压测量", itemPrice: 10, department: "内科检查", description: "血压测量" },
      { id: "21", itemCode: "INT003", itemName: "心电图", itemPrice: 35, department: "内科检查", description: "常规心电图检查" },
      { id: "22", itemCode: "INT004", itemName: "动态心电图", itemPrice: 150, department: "内科检查", description: "24小时动态心电图检查" },
      { id: "23", itemCode: "INT005", itemName: "肺功能", itemPrice: 80, department: "内科检查", description: "肺功能检查" },
      { id: "58", itemCode: "INT006", itemName: "动态血压", itemPrice: 120, department: "内科检查", description: "24小时动态血压监测" },
      { id: "59", itemCode: "INT007", itemName: "运动平板试验", itemPrice: 180, department: "内科检查", description: "运动平板心电图试验" },
      { id: "60", itemCode: "INT008", itemName: "心脏彩超负荷试验", itemPrice: 200, department: "内科检查", description: "心脏彩超负荷试验" },

      // 外科检查
      { id: "24", itemCode: "SUR001", itemName: "外科检查", itemPrice: 25, department: "外科检查", description: "外科常规体格检查" },
      { id: "25", itemCode: "SUR002", itemName: "肛肠检查", itemPrice: 40, department: "外科检查", description: "肛肠科检查" },
      { id: "61", itemCode: "SUR003", itemName: "乳腺触诊", itemPrice: 20, department: "外科检查", description: "乳腺触诊检查" },
      { id: "62", itemCode: "SUR004", itemName: "甲状腺触诊", itemPrice: 15, department: "外科检查", description: "甲状腺触诊检查" },

      // 妇科检查
      { id: "26", itemCode: "GYN001", itemName: "妇科检查", itemPrice: 35, department: "妇科检查", description: "妇科常规检查" },
      { id: "27", itemCode: "GYN002", itemName: "宫颈涂片", itemPrice: 45, department: "妇科检查", description: "宫颈细胞学检查" },
      { id: "28", itemCode: "GYN003", itemName: "阴道B超", itemPrice: 75, department: "妇科检查", description: "阴道B超检查" },
      { id: "29", itemCode: "GYN004", itemName: "乳腺钼靶", itemPrice: 120, department: "妇科检查", description: "乳腺X线摄影检查" },
      { id: "63", itemCode: "GYN005", itemName: "HPV检测", itemPrice: 150, department: "妇科检查", description: "人乳头瘤病毒检测" },
      { id: "64", itemCode: "GYN006", itemName: "TCT检查", itemPrice: 80, department: "妇科检查", description: "薄层液基细胞学检查" },

      // 眼科检查
      { id: "30", itemCode: "EYE001", itemName: "眼科检查", itemPrice: 20, department: "眼科检查", description: "眼科常规检查" },
      { id: "31", itemCode: "EYE002", itemName: "眼底检查", itemPrice: 40, department: "眼科检查", description: "眼底镜检查" },
      { id: "32", itemCode: "EYE003", itemName: "眼压测量", itemPrice: 25, department: "眼科检查", description: "眼压测量" },
      { id: "65", itemCode: "EYE004", itemName: "视野检查", itemPrice: 60, department: "眼科检查", description: "视野检查" },
      { id: "66", itemCode: "EYE005", itemName: "角膜地形图", itemPrice: 100, department: "眼科检查", description: "角膜地形图检查" },

      // 耳鼻喉科检查
      { id: "33", itemCode: "ENT001", itemName: "耳鼻喉科检查", itemPrice: 25, department: "耳鼻喉科检查", description: "耳鼻喉科常规检查" },
      { id: "34", itemCode: "ENT002", itemName: "听力检查", itemPrice: 50, department: "耳鼻喉科检查", description: "听力检查" },
      { id: "67", itemCode: "ENT003", itemName: "前庭功能检查", itemPrice: 80, department: "耳鼻喉科检查", description: "前庭功能检查" },
      { id: "68", itemCode: "ENT004", itemName: "鼻内镜检查", itemPrice: 60, department: "耳鼻喉科检查", description: "鼻内镜检查" },

      // 其他检查
      { id: "35", itemCode: "OTH001", itemName: "骨密度", itemPrice: 90, department: "其他检查", description: "骨密度检查" },
      { id: "36", itemCode: "OTH002", itemName: "幽门螺杆菌", itemPrice: 55, department: "其他检查", description: "幽门螺杆菌检查" },
      { id: "37", itemCode: "OTH003", itemName: "过敏原检测", itemPrice: 100, department: "其他检查", description: "过敏原检测" },
      { id: "38", itemCode: "OTH004", itemName: "微量元素", itemPrice: 70, department: "其他检查", description: "微量元素检查" },
      { id: "39", itemCode: "OTH005", itemName: "维生素D", itemPrice: 45, department: "其他检查", description: "维生素D检查" },
      { id: "40", itemCode: "OTH006", itemName: "同型半胱氨酸", itemPrice: 35, department: "其他检查", description: "同型半胱氨酸检查" },
      { id: "69", itemCode: "OTH007", itemName: "心理评估", itemPrice: 80, department: "其他检查", description: "心理健康评估" },
      { id: "70", itemCode: "OTH008", itemName: "睡眠监测", itemPrice: 200, department: "其他检查", description: "多导睡眠监测" },
      { id: "71", itemCode: "OTH009", itemName: "体成分分析", itemPrice: 50, department: "其他检查", description: "体成分分析检查" },
      { id: "72", itemCode: "OTH010", itemName: "动脉硬化检测", itemPrice: 120, department: "其他检查", description: "动脉硬化检测" }
    ];
  } catch (error) {
    console.error('获取体检项目失败:', error);
    ElMessage.error('获取体检项目失败');
  }
};

// 创建新模板
const createNewTemplate = () => {
  const newTemplate: AddItemTemplate = {
    id: Date.now().toString(),
    templateCode: `TPL${Date.now()}`,
    templateName: `新模板${templateList.value.length + 1}`,
    itemCount: 0,
    totalPrice: 0,
    status: 1,
    createTime: new Date().toISOString(),
    description: ''
  };

  selectedTemplate.value = newTemplate;
  templateForm.id = newTemplate.id;
  templateForm.templateName = newTemplate.templateName;
  templateForm.description = newTemplate.description || '';

  // 清空选择
  selectedItems.value = [];
  selectedItemIds.value = [];

  // 进入编辑模式
  isEditingName.value = true;

  ElMessage.success('已创建新模板，请编辑模板名称和选择项目');
};

// 点击行选择模板
const handleRowClick = (row: AddItemTemplate) => {
  selectedTemplate.value = row;
  templateForm.id = row.id;
  templateForm.templateName = row.templateName;
  templateForm.description = row.description || '';

  // 模拟获取模板包含的项目
  selectedItems.value = availableItems.value.slice(0, row.itemCount);
  selectedItemIds.value = selectedItems.value.map(item => item.id);

  // 退出编辑模式
  isEditingName.value = false;
};

// 编辑模板名称
const editTemplateName = () => {
  isEditingName.value = true;
};

// 取消编辑名称
const cancelEditName = () => {
  isEditingName.value = false;
  // 恢复原始名称
  if (selectedTemplate.value) {
    templateForm.templateName = selectedTemplate.value.templateName;
  }
};

// 保存模板名称
const saveTemplateName = async () => {
  if (!templateFormRef.value) return;

  try {
    await templateFormRef.value.validate();

    if (selectedTemplate.value) {
      selectedTemplate.value.templateName = templateForm.templateName;
      selectedTemplate.value.description = templateForm.description;
    }

    isEditingName.value = false;
    ElMessage.success('模板名称保存成功');
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 搜索项目
const onSearchItems = () => {
  // 搜索逻辑已在 computed 中实现
};

// 移除选中的项目
const removeSelectedItem = (item: TemplateItem) => {
  selectedItemIds.value = selectedItemIds.value.filter(id => id !== item.id);
};

// 保存模板项目
const saveTemplateItems = async () => {
  if (!selectedTemplate.value) {
    ElMessage.warning("请先选择模板");
    return;
  }

  if (selectedItems.value.length === 0) {
    ElMessage.warning("请选择要添加的体检项目");
    return;
  }

  try {
    // 更新模板信息
    selectedTemplate.value.itemCount = selectedItems.value.length;
    selectedTemplate.value.totalPrice = totalPrice.value;

    // 这里应该调用保存模板项目的API
    ElMessage.success("模板项目保存成功");
  } catch (error) {
    console.error("保存模板项目失败:", error);
    ElMessage.error("保存模板项目失败");
  }
};

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  fetchTemplateList();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  fetchTemplateList();
};
</script>

<style scoped lang="scss">
.add-item-template-container {
  .main-content {
    height: 80vh;
  }

  .template-list-card {
    height: 100%;

    .add-button-section {
      padding: 15px;
      margin-bottom: 15px;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }

    .search-form {
      margin-bottom: 15px;
      overflow: hidden;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgb(102 126 234 / 15%);

      .search-form-content {
        padding: 5px;
        background: rgb(255 255 255 / 95%);
        backdrop-filter: blur(10px);

        :deep(.el-form-item) {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        :deep(.el-form-item__label) {
          font-size: 13px;
          font-weight: 500;
          line-height: 32px;
          color: #606266;
        }

        :deep(.custom-input) {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
            }

            &.is-focus {
              box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
            }
          }

          .input-icon {
            font-size: 14px;
            color: #667eea;
          }
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding: 10px 0;
      margin-top: 15px;
      border-top: 1px solid #e4e7ed;
    }

    :deep(.el-table) {
      .el-table__row {
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .current-row {
        background-color: #ecf5ff !important;

        &:hover {
          background-color: #ecf5ff !important;
        }
      }
    }
  }

  .template-detail-card {
    height: 100%;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .template-items-container {
      display: flex;
      flex-direction: column;
      gap: 15px;
      height: calc(100% - 60px);

      .template-name-section {
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
      }

      .search-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;

        .action-buttons {
          display: flex;
          gap: 15px;
          padding: 0 15px;
        }
      }

      .items-selection-container {
        display: grid;
        flex: 1;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        min-height: 0;

        .items-list-section {
          display: flex;
          flex-direction: column;
          gap: 10px;
          height: 70vh;
          overflow-y: hidden;

          .section-title {
            padding: 8px 0;
            font-size: 14px;
            font-weight: bold;
            color: #303133;
          }

          .items-container {
            display: flex;
            flex: 1;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            overflow: auto;
            border: 1px solid var(--el-border-color);
            border-radius: 4px;

            .item-group {
              display: flex;
              flex-direction: column;
              gap: 10px;
            }

            .item-group-title {
              padding: 5px 0;
              font-size: 14px;
              font-weight: bold;
              color: #409eff;
              border-bottom: 1px solid #ebeef5;
            }

            .item-checkbox-group {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              margin-left: 20px;
            }

            .item-checkbox {
              margin-bottom: 10px;
            }
          }
        }

        .selected-items-section {
          display: flex;
          flex-direction: column;
          gap: 10px;

          .selected-header {
            display: grid;
            grid-template-columns: 86px 1fr 1fr;
            gap: 10px;
            align-items: center;
            padding: 6px 8px;
            font-size: 14px;
            font-weight: bold;
            background-color: var(--el-fill-color-light);
            border-radius: 4px;

            .selected-count {
              font-weight: normal;
            }

            .selected-price {
              color: var(--el-color-danger);
              text-align: right;
            }
          }
        }
      }
    }

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow-y: auto;
    }
  }

  .empty-card {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    :deep(.el-card__body) {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}

// 全局样式调整
:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        font-weight: bold;
        color: #303133;
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
