<template>
  <div class="add-item-template-container">
    <!-- 左右布局 -->
    <el-row :gutter="24" class="main-content">
      <!-- 左侧：加项模板列表 -->
      <el-col :span="6">
        <el-card class="template-list-card" shadow="never">
          <!-- 新增按钮 -->
          <div class="add-button-section">
            <el-button type="primary" @click="createNewTemplate" :icon="Plus" style="width: 100%">
              新增模板
            </el-button>
          </div>

          <!-- 查询表单 -->
          <div class="search-form">
            <el-form :model="searchForm" size="small" label-width="70px" class="search-form-content">
              <el-form-item label="模板名称" class="form-item">
                <el-input v-model="searchForm.templateName" placeholder="请输入模板名称关键词" clearable style="width: 100%"
                  class="custom-input" size="default">
                  <template #prefix>
                    <el-icon class="input-icon">
                      <Document />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>

          <!-- 加项模板表格 -->
          <el-table :data="templateList" border stripe v-loading="loading" :header-cell-style="{
            background: '#f5f7fa',
            color: '#303133',
            fontWeight: 'bold'
          }" @row-click="handleRowClick" :highlight-current-row="true" height="63vh">
            <el-table-column prop="templateName" label="模板名称" show-overflow-tooltip />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50]" layout="total, sizes, prev, pager, next" :total="pagination.total" background
              small @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：模板详情和项目设置 -->
      <el-col :span="18">
        <el-card class="template-detail-card" shadow="never" v-if="selectedTemplate">
          <template #header>
            <div class="card-header">
              <span class="title">模板项目设置 - {{ selectedTemplate.templateName }}</span>
              <div class="header-actions">
                <el-button size="small" @click="saveTemplateName" :disabled="!isEditingName">保存名称</el-button>
                <el-button size="small" @click="editTemplateName" v-if="!isEditingName">编辑名称</el-button>
                <el-button size="small" @click="cancelEditName" v-if="isEditingName">取消</el-button>
              </div>
            </div>
          </template>

          <div class="template-items-container">
            <!-- 模板名称编辑 -->
            <div class="template-name-section" v-if="isEditingName">
              <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="80px">
                <el-form-item label="模板名称" prop="templateName">
                  <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit
                    style="width: 300px" />
                </el-form-item>
              </el-form>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="search-section">
              <el-input v-model="itemSearchKeyword" placeholder="请输入项目名称" style="width: 500px">
                <template #prepend>
                  <el-select v-model="selectedCategory" placeholder="" style="width: 200px" @change="onSearchItems">
                    <el-option :label="item.label" :value="item.value" v-for="(item, index) in categoryOptions"
                      :key="index" />
                  </el-select>
                </template>
                <template #append>
                  <el-button :icon="Search" @click="onSearchItems" />
                </template>
              </el-input>
              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button type="primary" @click="saveTemplateItems" :disabled="selectedItems.length === 0">
                  保存模板项目
                </el-button>
                <el-button @click="selectedItems = []">清空选择</el-button>
              </div>
            </div>

            <!-- 体检项目选择区域 -->
            <div class="items-selection-container">
              <div class="items-list-section">
                <div class="section-title">可选体检项目</div>
                <div class="items-container">
                  <div v-for="(group, index) in filteredItems" :key="index" class="item-group">
                    <div class="item-group-title">
                      <el-check-tag checked>{{ group.department }}</el-check-tag>
                    </div>
                    <el-checkbox-group v-model="selectedItemIds" class="item-checkbox-group">
                      <el-checkbox v-for="item in group.items" :key="item.id" :label="item.id" border
                        class="item-checkbox">
                        {{ `${item.itemName}　　￥${item.itemPrice}` }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <el-empty v-if="filteredItems.length === 0" description="暂无数据" />
                </div>
              </div>

              <div class="selected-items-section">
                <div class="selected-header">
                  <span class="selected-title">已选择项目</span>
                  <span class="selected-count">共 {{ selectedItems.length }} 项</span>
                  <span class="selected-price">总价：{{ totalPrice.toFixed(2) }}元</span>
                </div>
                <el-table :data="selectedItems" style="width: 100%; overflow-y: hidden;" height="63vh" max-height="63vh"
                  border>
                  <el-table-column prop="itemCode" label="项目编码" width="120" />
                  <el-table-column prop="itemName" label="项目名称" />
                  <el-table-column prop="department" label="科室" width="100" />
                  <el-table-column prop="itemPrice" label="价格" width="100" />
                  <el-table-column fixed="right" label="操作" width="110">
                    <template #default="scope">
                      <el-button link type="danger" @click="removeSelectedItem(scope.row)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 未选择模板时的提示 -->
        <el-card class="empty-card" shadow="never" v-else>
          <el-empty description="请选择左侧模板或点击新增按钮创建模板" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts" name="addItemTemplate">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { Search, Refresh, Document, Plus } from '@element-plus/icons-vue';
import type { AddItemTemplate, TemplateItem, SearchForm, Pagination, TemplateForm } from './interface';
import { IndividualPackage, individualPackageApi } from "@/api";

// 搜索表单
const searchForm = reactive<SearchForm>({
  templateName: '',
  templateCode: ''
});

// 分页信息
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 表格数据
const templateList = ref<AddItemTemplate[]>([]);
const loading = ref(false);

// 选中的模板
const selectedTemplate = ref<AddItemTemplate | null>(null);

// 模板表单
const templateForm = reactive<TemplateForm>({
  id: '',
  templateName: '',
  description: ''
});

const templateFormRef = ref<FormInstance>();

// 表单验证规则
const templateRules = reactive<FormRules>({
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在2到50个字符', trigger: 'blur' }
  ]
});

// 编辑状态
const isEditingName = ref(false);

// 项目相关
const availableItems = ref<TemplateItem[]>([]);
const selectedItems = ref<TemplateItem[]>([]);
const selectedItemIds = ref<string[]>([]);
const itemSearchKeyword = ref('');
const selectedCategory = ref('-1');

// 体检项目组合数据
const combinationData = ref<IndividualPackage.CombsByItemCls[]>([]);
const filteredCombination = ref<IndividualPackage.CombsByItemCls[]>([]);
const selectedCombination = ref<string[]>([]);

// 额外加项数据
const extraCombinationData = ref<IndividualPackage.CombinationData[]>([]);
const selectedExtraCombination = ref<string[]>([]);

// 当前模板的套餐代码（用于保存额外加项）
const currentClusCode = ref<string>('');

// 科室分类选项 - 基于API数据的项目分类
const categoryOptions = computed(() => {
  const options = [
    { value: '-1', label: '全部项目分类' },
    ...combinationData.value.map(cls => ({ value: cls.clsCode, label: cls.clsName }))
  ];

  // 如果有额外加项数据，添加额外加项选项
  if (extraCombinationData.value.length > 0) {
    options.push({ value: 'extra', label: '额外加项' });
  }

  return options;
});

// 过滤后的项目列表（按项目分类分组）
const filteredItems = computed(() => {
  // 合并基础组合数据和额外加项数据
  let allItems: TemplateItem[] = [];

  // 添加基础组合数据
  combinationData.value.forEach(cls => {
    cls.combData.forEach(comb => {
      allItems.push({
        id: comb.combCode,
        itemCode: comb.combCode,
        itemName: comb.combName,
        itemPrice: comb.price,
        department: cls.clsName,
        description: `${cls.clsName} - ${comb.combName}`
      });
    });
  });

  // 添加额外加项数据
  extraCombinationData.value.forEach(comb => {
    allItems.push({
      id: `extra_${comb.combCode}`,
      itemCode: comb.combCode,
      itemName: `[额外加项] ${comb.combName}`,
      itemPrice: comb.price,
      department: '额外加项',
      description: `额外加项 - ${comb.combName}`
    });
  });

  let result = allItems;

  // 按项目分类过滤
  if (selectedCategory.value !== '-1') {
    result = result.filter(item => {
      if (selectedCategory.value === 'extra') {
        return item.department === '额外加项';
      }
      return item.department !== '额外加项' &&
             combinationData.value.some(cls => cls.clsCode === selectedCategory.value && cls.clsName === item.department);
    });
  }

  // 按关键字搜索
  if (itemSearchKeyword.value) {
    const keyword = itemSearchKeyword.value.toLowerCase();
    result = result.filter(item =>
      item.itemName.toLowerCase().includes(keyword) ||
      item.itemCode.toLowerCase().includes(keyword)
    );
  }

  // 按科室分组
  const groupedItems = result.reduce((groups, item) => {
    const department = item.department;
    if (!groups[department]) {
      groups[department] = [];
    }
    groups[department].push(item);
    return groups;
  }, {} as Record<string, TemplateItem[]>);

  // 转换为数组格式，每个元素包含科室名称和项目列表
  return Object.entries(groupedItems).map(([department, items]) => ({
    department,
    items
  }));
});

// 计算总价格
const totalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => total + item.itemPrice, 0);
});

// 监听选中项目变化
watch(selectedItemIds, (newIds) => {
  const allItems: TemplateItem[] = [];

  // 添加基础组合数据
  combinationData.value.forEach(cls => {
    cls.combData.forEach(comb => {
      allItems.push({
        id: comb.combCode,
        itemCode: comb.combCode,
        itemName: comb.combName,
        itemPrice: comb.price,
        department: cls.clsName,
        description: `${cls.clsName} - ${comb.combName}`
      });
    });
  });

  // 添加额外加项数据
  extraCombinationData.value.forEach(comb => {
    allItems.push({
      id: `extra_${comb.combCode}`,
      itemCode: comb.combCode,
      itemName: `[额外加项] ${comb.combName}`,
      itemPrice: comb.price,
      department: '额外加项',
      description: `额外加项 - ${comb.combName}`
    });
  });

  selectedItems.value = allItems.filter(item => newIds.includes(item.id));
});

// 初始化
onMounted(() => {
  fetchTemplateList();
  fetchAvailableItems();
  fetchExtraItems();
});

// 获取模板列表
const fetchTemplateList = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    setTimeout(() => {
      // 真实的加项模板数据
      const mockData = [
        {
          id: "1",
          templateCode: "TPL001",
          templateName: "基础体检模版",
          itemCount: 5,
          totalPrice: 280,
          status: 1,
          createTime: '2024-01-15 10:30:00',
          description: '包含基础体检常用加项项目'
        },
        {
          id: "2",
          templateCode: "TPL002",
          templateName: "心脑血管专项模版",
          itemCount: 8,
          totalPrice: 450,
          status: 1,
          createTime: '2024-01-20 14:20:00',
          description: '心脑血管疾病筛查专项模版'
        },
        {
          id: "3",
          templateCode: "TPL003",
          templateName: "肿瘤筛查模版",
          itemCount: 6,
          totalPrice: 380,
          status: 1,
          createTime: '2024-02-01 09:15:00',
          description: '常见肿瘤标志物筛查加项'
        },
        {
          id: "4",
          templateCode: "TPL004",
          templateName: "妇科专项模版",
          itemCount: 4,
          totalPrice: 220,
          status: 1,
          createTime: '2024-02-10 16:45:00',
          description: '妇科疾病筛查专项加项'
        },
        {
          id: "5",
          templateCode: "TPL005",
          templateName: "老年体检模版",
          itemCount: 7,
          totalPrice: 320,
          status: 1,
          createTime: '2024-02-15 11:30:00',
          description: '老年人群专项体检加项'
        },
        {
          id: "6",
          templateCode: "TPL006",
          templateName: "入职体检模版",
          itemCount: 3,
          totalPrice: 150,
          status: 1,
          createTime: '2024-02-20 13:20:00',
          description: '入职体检常用加项项目'
        },
        {
          id: "7",
          templateCode: "TPL007",
          templateName: "糖尿病筛查模版",
          itemCount: 5,
          totalPrice: 180,
          status: 1,
          createTime: '2024-03-01 10:00:00',
          description: '糖尿病相关指标筛查加项'
        },
        {
          id: "8",
          templateCode: "TPL008",
          templateName: "肝功能专项模版",
          itemCount: 6,
          totalPrice: 200,
          status: 1,
          createTime: '2024-03-05 15:30:00',
          description: '肝功能全面检查加项'
        },
        {
          id: "9",
          templateCode: "TPL009",
          templateName: "肾功能专项模版",
          itemCount: 4,
          totalPrice: 160,
          status: 1,
          createTime: '2024-03-10 12:15:00',
          description: '肾功能检查专项加项'
        },
        {
          id: "10",
          templateCode: "TPL010",
          templateName: "甲状腺功能模版",
          itemCount: 3,
          totalPrice: 120,
          status: 1,
          createTime: '2024-03-15 14:45:00',
          description: '甲状腺功能检查加项'
        },
        {
          id: "11",
          templateCode: "TPL011",
          templateName: "高血压筛查模版",
          itemCount: 4,
          totalPrice: 140,
          status: 1,
          createTime: '2024-03-20 09:30:00',
          description: '高血压相关指标筛查加项'
        },
        {
          id: "12",
          templateCode: "TPL012",
          templateName: "骨密度专项模版",
          itemCount: 2,
          totalPrice: 115,
          status: 1,
          createTime: '2024-03-25 16:20:00',
          description: '骨质疏松筛查专项加项'
        },
        {
          id: "13",
          templateCode: "TPL013",
          templateName: "呼吸系统模版",
          itemCount: 3,
          totalPrice: 165,
          status: 1,
          createTime: '2024-04-01 11:45:00',
          description: '呼吸系统疾病筛查加项'
        },
        {
          id: "14",
          templateCode: "TPL014",
          templateName: "消化系统模版",
          itemCount: 5,
          totalPrice: 195,
          status: 1,
          createTime: '2024-04-05 14:15:00',
          description: '消化系统疾病筛查加项'
        },
        {
          id: "15",
          templateCode: "TPL015",
          templateName: "免疫系统模版",
          itemCount: 4,
          totalPrice: 180,
          status: 1,
          createTime: '2024-04-10 10:30:00',
          description: '免疫系统功能检查加项'
        },
        {
          id: "16",
          templateCode: "TPL016",
          templateName: "营养状况模版",
          itemCount: 3,
          totalPrice: 130,
          status: 1,
          createTime: '2024-04-15 13:20:00',
          description: '营养状况评估加项'
        },
        {
          id: "17",
          templateCode: "TPL017",
          templateName: "职业病筛查模版",
          itemCount: 6,
          totalPrice: 240,
          status: 1,
          createTime: '2024-04-20 15:40:00',
          description: '职业病相关检查加项'
        },
        {
          id: "18",
          templateCode: "TPL018",
          templateName: "运动医学模版",
          itemCount: 4,
          totalPrice: 200,
          status: 1,
          createTime: '2024-04-25 12:10:00',
          description: '运动医学相关检查加项'
        },
        {
          id: "19",
          templateCode: "TPL019",
          templateName: "心理健康模版",
          itemCount: 2,
          totalPrice: 80,
          status: 1,
          createTime: '2024-05-01 09:50:00',
          description: '心理健康评估加项'
        },
        {
          id: "20",
          templateCode: "TPL020",
          templateName: "睡眠质量模版",
          itemCount: 3,
          totalPrice: 120,
          status: 1,
          createTime: '2024-05-05 16:30:00',
          description: '睡眠质量评估加项'
        }
      ];

      // 应用搜索过滤
      let filteredData = mockData;
      if (searchForm.templateName) {
        filteredData = mockData.filter(item =>
          item.templateName.includes(searchForm.templateName)
        );
      }

      const start = (pagination.currentPage - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      templateList.value = filteredData.slice(start, end);
      pagination.total = filteredData.length;
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取模板列表失败');
    loading.value = false;
  }
};

// 获取可用体检项目
const fetchAvailableItems = async () => {
  try {
    // 调用 getCombsByItemCls API 获取体检项目组合数据
    const { data } = await individualPackageApi.getCombsByItemCls();
    combinationData.value = data;
    filteredCombination.value = data;

    // 为了兼容现有逻辑，将API数据转换为原有的availableItems格式
    const allItems: TemplateItem[] = [];
    data.forEach(cls => {
      cls.combData.forEach(comb => {
        allItems.push({
          id: comb.combCode,
          itemCode: comb.combCode,
          itemName: comb.combName,
          itemPrice: comb.price,
          department: cls.clsName,
          description: `${cls.clsName} - ${comb.combName}`
        });
      });
    });
    availableItems.value = allItems;

  } catch (error) {
    console.error('获取体检项目失败:', error);
    ElMessage.error('获取体检项目失败，使用默认数据');

  }
};

// 获取额外加项数据
const fetchExtraItems = async (templateId?: string) => {
  try {
    // 根据模板ID生成套餐代码，或使用已存在的套餐代码
    const clusCode = currentClusCode.value || (templateId ? `TEMPLATE_${templateId}` : 'DEMO_CLUSTER');

    // 调用 getClusterExtraComb API 获取额外加项数据
    const { data } = await individualPackageApi.getClusterExtraComb({ clusCode });
    extraCombinationData.value = data;

    // 保存套餐代码
    if (!currentClusCode.value) {
      currentClusCode.value = clusCode;
    }

    if (data.length > 0) {
      ElMessage.success(`额外加项数据加载成功，共 ${data.length} 项`);
    }
  } catch (error) {
    console.error('获取额外加项失败:', error);
    ElMessage.warning('获取额外加项失败，将只显示基础项目');

    // 如果API调用失败，设置为空数组
    extraCombinationData.value = [];
  }
};

// 创建新模板
const createNewTemplate = () => {
  const newTemplate: AddItemTemplate = {
    id: Date.now().toString(),
    templateCode: `TPL${Date.now()}`,
    templateName: `新模板${templateList.value.length + 1}`,
    itemCount: 0,
    totalPrice: 0,
    status: 1,
    createTime: new Date().toISOString(),
    description: ''
  };

  selectedTemplate.value = newTemplate;
  templateForm.id = newTemplate.id;
  templateForm.templateName = newTemplate.templateName;
  templateForm.description = newTemplate.description || '';

  // 设置当前套餐代码
  currentClusCode.value = `TEMPLATE_${newTemplate.id}`;

  // 清空选择
  selectedItems.value = [];
  selectedItemIds.value = [];

  // 进入编辑模式
  isEditingName.value = true;

  ElMessage.success('已创建新模板，请编辑模板名称和选择项目');
};

// 点击行选择模板
const handleRowClick = (row: AddItemTemplate) => {
  selectedTemplate.value = row;
  templateForm.id = row.id;
  templateForm.templateName = row.templateName;
  templateForm.description = row.description || '';

  // 设置当前套餐代码并获取额外加项数据
  currentClusCode.value = `TEMPLATE_${row.id}`;
  fetchExtraItems(row.id);

  // 模拟获取模板包含的项目
  selectedItems.value = availableItems.value.slice(0, row.itemCount);
  selectedItemIds.value = selectedItems.value.map(item => item.id);

  // 退出编辑模式
  isEditingName.value = false;
};

// 编辑模板名称
const editTemplateName = () => {
  isEditingName.value = true;
};

// 取消编辑名称
const cancelEditName = () => {
  isEditingName.value = false;
  // 恢复原始名称
  if (selectedTemplate.value) {
    templateForm.templateName = selectedTemplate.value.templateName;
  }
};

// 保存模板名称
const saveTemplateName = async () => {
  if (!templateFormRef.value) return;

  try {
    await templateFormRef.value.validate();

    if (selectedTemplate.value) {
      selectedTemplate.value.templateName = templateForm.templateName;
      selectedTemplate.value.description = templateForm.description;
    }

    isEditingName.value = false;
    ElMessage.success('模板名称保存成功');
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 搜索项目
const onSearchItems = () => {
  // 搜索逻辑已在 computed 中实现
};

// 移除选中的项目
const removeSelectedItem = (item: TemplateItem) => {
  selectedItemIds.value = selectedItemIds.value.filter(id => id !== item.id);
};

// 保存模板项目
const saveTemplateItems = async () => {
  if (!selectedTemplate.value) {
    ElMessage.warning("请先选择模板");
    return;
  }

  if (selectedItems.value.length === 0) {
    ElMessage.warning("请选择要添加的体检项目");
    return;
  }

  try {
    // 分离基础项目和额外加项
    const basicItems = selectedItems.value.filter(item => !item.id.startsWith('extra_'));
    const extraItems = selectedItems.value.filter(item => item.id.startsWith('extra_'));

    // 保存基础项目（如果有的话）
    if (basicItems.length > 0) {
      // 这里可以调用保存基础项目的API
      console.log('保存基础项目:', basicItems);
    }

    // 保存额外加项（如果有的话）
    if (extraItems.length > 0) {
      // 生成或使用现有的套餐代码
      const templateClusCode = currentClusCode.value || `TEMPLATE_${selectedTemplate.value?.id || Date.now()}`;

      const extraParams = extraItems.map(item => ({
        clusCode: templateClusCode,
        combCode: item.itemCode,
        price: item.itemPrice
      }));

      await individualPackageApi.saveClusterExtraComb(extraParams);

      // 保存套餐代码以便后续使用
      currentClusCode.value = templateClusCode;

      ElMessage.success(`成功保存 ${extraItems.length} 个额外加项`);
    }

    // 更新模板信息
    selectedTemplate.value.itemCount = selectedItems.value.length;
    selectedTemplate.value.totalPrice = totalPrice.value;

    ElMessage.success("模板项目保存成功");
  } catch (error) {
    console.error("保存模板项目失败:", error);
    ElMessage.error("保存模板项目失败");
  }
};

// 批量保存选中的体检项目
const saveSelectedItems = async () => {
  if (!selectedTemplate.value) {
    ElMessage.warning("请先选择模板");
    return;
  }

  if (selectedItems.value.length === 0) {
    ElMessage.warning("请选择要保存的体检项目");
    return;
  }

  try {
    // 分离基础项目和额外加项
    const basicItems = selectedItems.value.filter(item => !item.id.startsWith('extra_'));
    const extraItems = selectedItems.value.filter(item => item.id.startsWith('extra_'));

    let savedCount = 0;

    // 保存额外加项
    if (extraItems.length > 0) {
      const templateClusCode = currentClusCode.value || `TEMPLATE_${selectedTemplate.value.id}`;

      const extraParams = extraItems.map(item => ({
        clusCode: templateClusCode,
        combCode: item.itemCode,
        price: item.itemPrice
      }));

      await individualPackageApi.saveClusterExtraComb(extraParams);
      savedCount += extraItems.length;

      // 保存套餐代码
      currentClusCode.value = templateClusCode;
    }

    // 这里可以添加保存基础项目的逻辑
    if (basicItems.length > 0) {
      // TODO: 调用保存基础项目的API
      console.log('基础项目待保存:', basicItems);
      savedCount += basicItems.length;
    }

    // 更新模板信息
    selectedTemplate.value.itemCount = selectedItems.value.length;
    selectedTemplate.value.totalPrice = totalPrice.value;

    ElMessage.success(`成功保存 ${savedCount} 个体检项目`);
  } catch (error) {
    console.error("保存体检项目失败:", error);
    ElMessage.error("保存体检项目失败");
  }
};

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  fetchTemplateList();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  fetchTemplateList();
};
</script>

<style scoped lang="scss">
.add-item-template-container {
  .main-content {
    height: 80vh;
  }
  .template-list-card {
    height: 100%;
    .add-button-section {
      padding: 15px;
      margin-bottom: 15px;
    }
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }
    .search-form {
      margin-bottom: 15px;
      overflow: hidden;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgb(102 126 234 / 15%);
      .search-form-content {
        padding: 5px;
        background: rgb(255 255 255 / 95%);
        backdrop-filter: blur(10px);
        :deep(.el-form-item) {
          margin-bottom: 16px;
          &:last-child {
            margin-bottom: 0;
          }
        }
        :deep(.el-form-item__label) {
          font-size: 13px;
          font-weight: 500;
          line-height: 32px;
          color: #606266;
        }
        :deep(.custom-input) {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
            }
            &.is-focus {
              box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
            }
          }
          .input-icon {
            font-size: 14px;
            color: #667eea;
          }
        }
      }
    }
    .pagination-container {
      display: flex;
      justify-content: center;
      padding: 10px 0;
      margin-top: 15px;
      border-top: 1px solid #e4e7ed;
    }
    :deep(.el-table) {
      .el-table__row {
        cursor: pointer;
        &:hover {
          background-color: #f5f7fa;
        }
      }
      .current-row {
        background-color: #ecf5ff !important;
        &:hover {
          background-color: #ecf5ff !important;
        }
      }
    }
  }
  .template-detail-card {
    height: 100%;
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
    .template-items-container {
      display: flex;
      flex-direction: column;
      gap: 15px;
      height: calc(100% - 60px);
      .template-name-section {
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
      }
      .search-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;
        .action-buttons {
          display: flex;
          gap: 15px;
          padding: 0 15px;
        }
      }
      .items-selection-container {
        display: grid;
        flex: 1;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        min-height: 0;
        .items-list-section {
          display: flex;
          flex-direction: column;
          gap: 10px;
          height: 70vh;
          overflow-y: hidden;
          .section-title {
            padding: 8px 0;
            font-size: 14px;
            font-weight: bold;
            color: #303133;
          }
          .items-container {
            display: flex;
            flex: 1;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            overflow: auto;
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            .item-group {
              display: flex;
              flex-direction: column;
              gap: 10px;
            }
            .item-group-title {
              padding: 5px 0;
              font-size: 14px;
              font-weight: bold;
              color: #409eff;
              border-bottom: 1px solid #ebeef5;
            }
            .item-checkbox-group {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              margin-left: 20px;
            }
            .item-checkbox {
              margin-bottom: 10px;
            }
          }
        }
        .selected-items-section {
          display: flex;
          flex-direction: column;
          gap: 10px;
          .selected-header {
            display: grid;
            grid-template-columns: 86px 1fr 1fr;
            gap: 10px;
            align-items: center;
            padding: 6px 8px;
            font-size: 14px;
            font-weight: bold;
            background-color: var(--el-fill-color-light);
            border-radius: 4px;
            .selected-count {
              font-weight: normal;
            }
            .selected-price {
              color: var(--el-color-danger);
              text-align: right;
            }
          }
        }
      }
    }
    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow-y: auto;
    }
  }
  .empty-card {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    :deep(.el-card__body) {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}

// 全局样式调整
:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        font-weight: bold;
        color: #303133;
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
