<template>
  <div>
    <form-container v-model="visible" :title="`${projectClassificationProps.opt}分类`" form-size="800px" @close="onClose">
      <el-form
        class="form"
        ref="projectClassificationFormRef"
        :rules="rules"
        :disabled="projectClassificationProps.disabled"
        :model="projectClassificationProps.record"
        :hide-required-asterisk="projectClassificationProps.disabled"
        label-width="auto"
        label-suffix=" :"
      >
        <s-form-item label="分类编码" prop="clsCode">
          <s-input
            v-model="projectClassificationProps.record.clsCode"
            placeholder="请输入分类编码"
            :disabled="projectClassificationProps.opt === FormOptEnum.EDIT"
          ></s-input>
        </s-form-item>
        <s-form-item label="分类名称" prop="clsName">
          <s-input
            v-model="projectClassificationProps.record.clsName"
            placeholder="请输入分类名称"
            :disabled="projectClassificationProps.opt === FormOptEnum.EDIT"
          ></s-input>
        </s-form-item>
        <s-form-item label="分类简称" prop="shortName">
          <s-input v-model="projectClassificationProps.record.shortName" placeholder="请输入分类简称"></s-input>
        </s-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!projectClassificationProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { ProjectClassification, projectClassificationApi } from "@/api";
import { FormOptEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";

const visible = ref(false); //是否显示表单

// 表单参数
const projectClassificationProps = reactive<FormProps.Base<ProjectClassification.ProjectClassificationInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  clsCode: [required("请输入分类编码")],
  clsName: [required("请输入分类名称")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<ProjectClassification.ProjectClassificationInfo>) {
  Object.assign(projectClassificationProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    projectClassificationProps.record = {
      id: "",
      clsCode: "",
      clsName: "",
      shortName: ""
    };
  }
  visible.value = true; //显示表单
  if (props.record.id) {
    projectClassificationProps.record = JSON.parse(JSON.stringify(props.record));
  }
}

// 提交数据（新增/编辑）
const projectClassificationFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  projectClassificationFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    await projectClassificationApi
      .submitForm(projectClassificationProps.record, projectClassificationProps.record.id != undefined)
      .then(() => {
        ElMessage.success("保存成功");
        projectClassificationProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container {
  .form {
    margin-top: 20px;
  }
  :deep(.el-dialog__body) {
    margin-top: 0;
  }
  :deep(.el-drawer__body) {
    margin-top: -20px;
  }
}
</style>
