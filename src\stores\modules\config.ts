/**
 * @description 系统配置
 */

import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";
import { SysConfig, commonApi, SysOrg } from "@/api";
import { SysBaseEnum } from "@/enums";

const name = "base-config"; // 定义模块名称

export interface SysConfigState {
  /** 系统基本信息 */
  sysInfo: SysConfig.SysBaseConfig;
  /** 机构code,用来下次登录自动选择机构 */
  orgInfo: SysOrg.SysOrgInfo | null;
}

/** 配置模块 */
export const useConfigStore = defineStore({
  id: name,
  state: (): SysConfigState => ({
    sysInfo: {
      SYS_NAME: "",
      SYS_LOGO: "",
      SYS_ICO: "",
      SYS_VERSION: "",
      SYS_COPYRIGHT: "",
      SYS_COPYRIGHT_URL: "",
      SYS_FOOTER_LINKS: [],
      SYS_TENANT_OPTIONS: "CHOSE"
    },
    orgInfo: null
  }),
  getters: {
    sysBaseInfoGet: state => state.sysInfo,
    orgInfoGet: state => state.orgInfo
  },
  actions: {
    /**  设置系统基本信息 */
    async setSysBaseInfo() {
      /**  获取系统基本信息 */
      const { data } = await commonApi.sysInfo();
      if (data) {
        //sysConfigProps赋值
        data.forEach((item: SysConfig.ConfigInfo) => {
          //如果是对象类型的属性就转成对象
          if (item.configKey == SysBaseEnum.SYS_FOOTER_LINKS) {
            const footerLinks: SysConfig.FooterLinkProps[] = JSON.parse(item.configValue);
            this.sysInfo.SYS_FOOTER_LINKS = footerLinks;
          } else {
            // 其他属性直接赋值
            (this.sysInfo[item.configKey as keyof SysConfig.SysBaseConfig] as string) = item.configValue;
          }
        });
      }
      return this.sysInfo;
    },
    /** 获取系统基本信息 */
    async getSysBaseInfo() {
      if (this.sysInfo.SYS_NAME != "") {
        return this.sysInfo;
      } else {
        return await this.setSysBaseInfo();
      }
    },
    /** 设置机构code */
    setOrgInfo(Info: SysOrg.SysOrgInfo | null) {
      this.orgInfo = Info;
    },
    /** 删除机构code */
    delOrgInfoe() {
      this.orgInfo = null;
    }
  },
  persist: piniaPersistConfig(name)
});
