<template>
  <div class="global-number-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="date-navigation">
        <el-button type="primary" icon="ArrowLeft" size="small" plain @click="prevMonth"> 上个月 </el-button>
        <el-button type="primary" size="small" plain @click="goToday"> 今天 </el-button>
        <el-button type="primary" icon="ArrowRight" size="small" plain @click="nextMonth"> 下个月 </el-button>
      </div>

      <div class="date-picker">
        <el-date-picker
          v-model="selectedMonth"
          style="width: 140px"
          type="month"
          placeholder="选择月份"
          format="YYYY年MM月"
          value-format="YYYY-MM"
          @change="getAppointmentData"
        />
      </div>

      <div class="data-select" v-if="props.isCompany">
        <label>单位：</label>
        <el-select filterable clearable v-model="searchInfo.unitCode" placeholder="请选择单位" @change="getAppointmentData">
          <el-option v-for="item in unitList" :key="item.companyCode" :label="item.companyName" :value="item.companyCode" />
        </el-select>
      </div>
      <div class="data-select" v-if="props.isNumberSource">
        <label>重点项目：</label>
        <el-select filterable clearable v-model="searchInfo.codeItemCode" placeholder="请选择重点项目" @change="getAppointmentData">
          <el-select clearable v-model="form.codeItemCode" placeholder="请选择重点项目">
            <el-option v-for="item in combList" :key="item.combCode" :label="item.combName" :value="item.combCode" />
          </el-select>
        </el-select>
      </div>
      <div class="actions"></div>

      <div class="legend">
        <div class="legend-item">
          <div class="legend-color holiday"></div>
          <span>节假日/休息日</span>
        </div>
      </div>
    </div>

    <div class="month-indicator">{{ displayMonth }} 体检号源预约情况</div>

    <!-- 表格区域 -->
    <div class="scroll-container" v-if="C_numberSourceData.firstHalfDates.length > 0">
      <div class="table-header">
        <div class="fixed-label">日期</div>
        <div class="dates-container">
          <!-- 第一行日期 -->
          <div v-for="(item, index) in C_numberSourceData.firstHalfDates" :key="index" class="date-header">
            {{ extractYearMonth(item.date).day }}
          </div>
        </div>
      </div>

      <div class="table-body">
        <!-- 第一行星期 -->
        <div class="data-row">
          <div class="row-label">星期</div>
          <div class="row-data">
            <div v-for="(item, index) in C_numberSourceData.firstHalfDates" :key="index" class="data-cell">
              {{ getDayOfWeek(item.week) }}
            </div>
          </div>
        </div>

        <!-- 第一行总号源 -->
        <div class="data-row">
          <div class="row-label">总号源</div>
          <div class="row-data">
            <div
              v-for="(item, index) in C_numberSourceData.firstHalfDates"
              :key="index"
              class="data-cell"
              :class="{
                'holiday-cell': item.isVacation === 'T'
              }"
              @click="editNumberSource(item)"
            >
              <span v-if="item.isVacation === 'T'">休假</span>
              <span v-else>{{ item.totalCapacity }}</span>
            </div>
          </div>
        </div>

        <!-- 第一行全部已约 -->
        <div class="data-row">
          <div class="row-label">全部已约</div>
          <div class="row-data">
            <div
              v-for="(item, index) in C_numberSourceData.firstHalfDates"
              :key="index"
              class="data-cell"
              :class="{
                'holiday-cell': item.isVacation === 'T'
              }"
              @click="editNumberSource(item)"
            >
              <span v-if="item.isVacation === 'T'">休假</span>
              <span v-else>
                {{ item.usedCapacity }}
              </span>
            </div>
          </div>
        </div>

        <!-- 第一行全部剩余 -->
        <div class="data-row">
          <div class="row-label">全部剩余</div>
          <div class="row-data">
            <div
              v-for="(item, index) in C_numberSourceData.firstHalfDates"
              :key="index"
              class="data-cell"
              :class="{
                'holiday-cell': item.isVacation === 'T'
              }"
              @click="editNumberSource(item)"
            >
              <span v-if="item.isVacation === 'T'">休假</span>
              <span v-else>
                {{ item.availableCapacity }}
              </span>
            </div>
          </div>
        </div>

        <div v-if="C_numberSourceData.secondHalfDates.length > 0">
          <!-- 表头第二行 -->
          <div class="table-header">
            <div class="fixed-label">日期</div>
            <div class="dates-container">
              <!-- 第二行日期 -->
              <div v-for="(item, index) in C_numberSourceData.secondHalfDates" :key="index" class="date-header">
                {{ extractYearMonth(item.date).day }}
              </div>
            </div>
          </div>

          <!-- 第二行星期 -->
          <div class="data-row">
            <div class="row-label">星期</div>
            <div class="row-data">
              <div v-for="(item, index) in C_numberSourceData.secondHalfDates" :key="index" class="data-cell">
                {{ getDayOfWeek(item.week) }}
              </div>
            </div>
          </div>

          <!-- 第二行总号源 -->
          <div class="data-row">
            <div class="row-label">总号源</div>
            <div class="row-data">
              <div
                v-for="(item, index) in C_numberSourceData.secondHalfDates"
                :key="index"
                class="data-cell"
                :class="{
                  'holiday-cell': item.isVacation === 'T'
                }"
                @click="editNumberSource(item)"
              >
                <span v-if="item.isVacation === 'T'">休假</span>
                <span v-else>{{ item.totalCapacity }}</span>
              </div>
            </div>
          </div>

          <!-- 第二行全部已约 -->
          <div class="data-row">
            <div class="row-label">全部已约</div>
            <div class="row-data">
              <div
                v-for="(item, index) in C_numberSourceData.secondHalfDates"
                :key="index"
                class="data-cell"
                :class="{
                  'holiday-cell': item.isVacation === 'T'
                }"
                @click="editNumberSource(item)"
              >
                <span v-if="item.isVacation === 'T'">休假</span>
                <span v-else>
                  {{ item.usedCapacity }}
                </span>
              </div>
            </div>
          </div>

          <!-- 第二行全部剩余 -->
          <div class="data-row">
            <div class="row-label">全部剩余</div>
            <div class="row-data">
              <div
                v-for="(item, index) in C_numberSourceData.secondHalfDates"
                :key="index"
                class="data-cell"
                :class="{
                  'holiday-cell': item.isVacation === 'T'
                }"
                @click="editNumberSource(item)"
              >
                <span v-if="item.isVacation === 'T'">休假</span>
                <span v-else>
                  {{ item.availableCapacity }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="scroll-container">
      <el-empty description="暂无数据" />
    </div>
    <el-dialog v-model="dialogVisible" :title="form.title" :close-on-click-modal="false" :before-close="handleClose" width="600">
      <el-form ref="ref_form" :rules="rules" :model="form" label-width="auto" class="form-box" v-loading="dialogLoading">
        <el-form-item label="日期" prop="time">
          <el-date-picker
            v-model="form.time"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            unlink-panels
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>

        <el-form-item label="是否休假">
          <el-switch v-model="form.statu" prop="statu" />
        </el-form-item>
        <!-- <el-form-item label="总号源" prop="totalCapacity">
          <el-input-number placeholder="请输入总号源" :controls="false" :min="0"  v-model="form.totalCapacity"></el-input-number>
        </el-form-item> -->
        <el-form-item label="单位" prop="comanyCode" v-if="props.isCompany">
          <el-select filterable clearable v-model="form.comanyCode" placeholder="请选择单位">
            <el-option v-for="item in unitList" :key="item.companyCode" :label="item.companyName" :value="item.companyCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="重点项目" prop="codeItemCode" v-if="props.isNumberSource">
          <el-select filterable clearable v-model="form.codeItemCode" placeholder="请选择重点项目">
            <el-option v-for="item in combList" :key="item.combCode" :label="item.combName" :value="item.combCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="timeSlotID">
          <el-table border :data="tableData" style="">
            <el-table-column prop="timeSlotName" label="" width="150"> </el-table-column>
            <el-table-column prop="totalCapacity" label="预留人数">
              <template #default="scope">
                <el-input-number style="width: 100%" placeholder="" :controls="false" :min="0" v-model="scope.row.totalCapacity"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="usedCapacity" label="已约人数" />
            <el-table-column prop="availableCapacity" label="剩余人数" />
          </el-table>
        </el-form-item>
        <el-form-item>
          <el-row style="width: 100%" justify="end">
            <el-button type="primary" @click="submit">确定</el-button>
            <el-button @click="handleClose">取消</el-button>
          </el-row>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="numberSource">
import { ref, computed, onMounted, withDefaults, defineProps, reactive } from "vue";
import {
  CombinationManagement,
  combinationManagementApi,
  CompanyManagement,
  companyManagementApi,
  numberSource,
  numberSourceApi,
  ResultData,
  TimeSlots,
  timeSlotsApi
} from "@/api";

const props = defineProps({
  sourceTypeID: {
    type: String,
    required: true
  },
  isCompany: {
    type: Boolean,
    default: false
  },
  isNumberSource: {
    type: Boolean,
    default: false
  }
});

interface formType {
  title?: string;
  time?: string[];
  timeSlotID?: string;
  statu?: boolean;
  totalCapacity?: number;
  comanyCode?: string;
  codeItemCode?: string;
}

let form = reactive<formType>({});

const ref_form = ref();
const rules = reactive({
  time: [{ required: true, message: "日期不能为空", trigger: "blur" }],
  // timeSlotID: [
  //   { required: true, message: '时段不能为空', trigger: 'blur' },
  // ],
  // totalCapacity: [
  //   { required: true, message: '总号源不能为空', trigger: 'blur' },
  // ],
  // statu: [
  //   { required: true, message: '休假不能为空', trigger: 'blur' },
  // ],
  comanyCode: [{ required: true, message: "单位不能为空", trigger: "blur" }],
  codeItemCode: [{ required: true, message: "重点项目不能为空", trigger: "blur" }]
});

// 提交表单数据
const submit = () => {
  ref_form.value.validate(async valid => {
    if (valid) {
      const params = tableData.value.map(item => {
        return {
          comanyCode: props.isCompany ? form.comanyCode : undefined,
          codeItemCode: props.isNumberSource ? form.codeItemCode : undefined,
          sourceTypeID: props.sourceTypeID,
          startTime: form.time[0],
          endTime: form.time[1],
          timeSlotID: item.timeSlotEntryID,
          statu: form.statu ? "T" : "F",
          totalCapacity: item.totalCapacity
        };
      });
      const { code, msg } = await numberSourceApi.editNumberSource(params);

      if (code === 200) {
        handleClose();
        getAppointmentData();
      } else {
        ElMessage.error(msg);
      }
    } else {
      console.log("表单验证失败");
    }
  });
};

//清空表单
const resetForm = () => {
  form.title = "";
  form.time = null;
  form.timeSlotID = "";
  form.statu = false;
  form.totalCapacity = 0;
  form.comanyCode = "";
  form.codeItemCode = "";
  tableData.value = [];
};
let dialogVisible = ref(false);

//关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

//编辑号源
const editNumberSource = (item: numberSource.SourceList) => {
  form.title = "编辑";
  form.time = [item.date, item.date];
  form.statu = item.isVacation === "T";
  dialogVisible.value = true;
  getNumberSourceByDate(item);
};

let dialogLoading = ref(false);
let tableData = ref<TimeSlots.SpecifiedTimeSlotInfo[] | numberSource.SourceList[]>([]);
//获取某一天号源
const getNumberSourceByDate = async item => {
  dialogLoading.value = true;
  try {
    const { year, month, day } = extractYearMonth(item.date);
    const { data, code } = await numberSourceApi.getNumberSourceByDate({
      unitCode: searchInfo.unitCode || undefined,
      codeItemCode: searchInfo.codeItemCode || undefined,
      sourceTypeID: props.sourceTypeID,
      year,
      month,
      day
    });

    if (Number(code) === 200) {
      tableData.value = timeSlotsList.value.map(item => {
        let dataFind = data?.find(itemData => itemData.timeSlotID === item.timeSlotEntryID);
        return {
          ...item,
          ...(dataFind || {
            totalCapacity: 0,
            usedCapacity: 0,
            availableCapacity: 0,
            isVacation: form.statu ? "T" : "F"
          })
        };
      });

      // Array.isArray(data) ? data : [];
    }
  } finally {
    dialogLoading.value = false;
  }
};

const timePeriodData = data => {
  let timeSlots = timeSlotsList.value.find(item => item.timeSlotEntryID === data);
  return `${timeSlots.startTime}-${timeSlots.endTime}`;
};

// 当前选中的月份
const selectedMonth = ref<string>(getCurrentMonth());

let searchInfo = reactive({
  unitCode: "",
  codeItemCode: "",
  year: "",
  month: ""
});

let unitList = ref<CompanyManagement.Company[]>([]);

//获取单位列表
const getUnitList = async () => {
  const { data, code } = await companyManagementApi.page({
    pageNum: 0,
    pageSize: 2000,
    searchKey: ""
  });
  if (code === 200) {
    unitList.value = data.list;
  }
};

let combList = ref<CombinationManagement.CombinationInfo[]>([]);
//获取组合列表
const getCombList = async () => {
  const { data, code } = await combinationManagementApi.getCombination();
  if (code === 200) {
    combList.value = data;
  }
};

let timeSlotsList = ref<TimeSlots.SpecifiedTimeSlotInfo[]>([]);
//获取时段列表
const gettimeSlotsList = async () => {
  const { data, code } = await timeSlotsApi.getTimeSlotByType({ sourceTypeID: props.sourceTypeID });
  if (code === 200) {
    timeSlotsList.value = data.filter(item => item.statu === "T");
  }
};

let numberSourceData = ref<numberSource.SourceList[] | []>([]);
// 生成月份完整日期数组
const generateMonthDates = (year, month) => {
  const daysInMonth = new Date(year, month, 0).getDate();
  return Array.from({ length: daysInMonth }, (_, i) => {
    const date = new Date(year, month - 1, i + 1);
    return {
      date: `${year}-${String(month).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`,
      week: date.getDay() || 7, // 周一到周日对应1-7
      isVacation: false,
      totalCapacity: "",
      usedCapacity: "",
      availableCapacity: "",
      timeSlotID: ""
    };
  });
};

//获取号源列表
const getAppointmentData = async () => {
  const [year, month] = selectedMonth.value.split("-");
  // 先生成完整月份日期数组
  const fullMonthDates = generateMonthDates(year, month);

  const { data, code } = await numberSourceApi.getNumberSource({
    unitCode: searchInfo.unitCode || undefined,
    codeItemCode: searchInfo.codeItemCode || undefined,
    sourceTypeID: props.sourceTypeID,
    year,
    month
  });

  if (code === 200) {
    // 将接口数据映射到对应日期
    const apiDataMap = new Map(Array.isArray(data) ? data.map(item => [item.date.split(" ")[0], item]) : []);

    numberSourceData.value = fullMonthDates.map(date => {
      const apiData = apiDataMap.get(date.date);
      return {
        ...date,
        ...(apiData || {}),
        // 确保休假状态正确
        isVacation: apiData ? apiData.isVacation : date.isVacation
      };
    });
  }
};

// 分割日期为两行
const C_numberSourceData = computed(() => {
  const midIndex = Math.ceil(numberSourceData.value.length / 2);
  return {
    firstHalfDates: numberSourceData.value.slice(0, midIndex),
    secondHalfDates: numberSourceData.value.slice(midIndex)
  };
});

// 显示当前月份
const displayMonth = computed<string>(() => {
  if (!selectedMonth.value) return "";
  const [year, month] = selectedMonth.value.split("-");
  return `${year}年${month}月`;
});

function getCurrentMonth(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;
}

// 判断是否是今天
function isToday(dateStr: string): boolean {
  const today = new Date();
  const date = new Date(dateStr);
  return date.getDate() === today.getDate() && date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();
}

// 获取星期
function getDayOfWeek(dateStr: number): string {
  const days = {
    1: "周一",
    2: "周二",
    3: "周三",
    4: "周四",
    5: "周五",
    6: "周六",
    7: "周日"
  };
  return days[dateStr] || dateStr;
}

/**
 * 从日期字符串中提取年月日
 * @param dateStr 日期字符串，格式如"2025-06-08 00:00:00"
 * @returns 包含year、month和day的对象
 */
function extractYearMonth(dateStr: string): { year: string; month: string; day: string } {
  const date = new Date(dateStr);
  return {
    year: date.getFullYear().toString(),
    month: String(date.getMonth() + 1).padStart(2, "0"),
    day: String(date.getDate()).padStart(2, "0")
  };
}

// 上一个月
function prevMonth(): void {
  const [year, month] = selectedMonth.value.split("-");
  let prevYear = parseInt(year);
  let prevMonth = parseInt(month) - 1;

  if (prevMonth === 0) {
    prevYear--;
    prevMonth = 12;
  }

  selectedMonth.value = `${prevYear}-${String(prevMonth).padStart(2, "0")}`;
  getAppointmentData();
}

// 下一个月
function nextMonth(): void {
  const [year, month] = selectedMonth.value.split("-");
  let nextYear = parseInt(year);
  let nextMonth = parseInt(month) + 1;

  if (nextMonth === 13) {
    nextYear++;
    nextMonth = 1;
  }

  selectedMonth.value = `${nextYear}-${String(nextMonth).padStart(2, "0")}`;
  getAppointmentData();
}

// 跳转到今天
function goToday(): void {
  selectedMonth.value = getCurrentMonth();
  getAppointmentData();
}

onMounted(() => {
  getAppointmentData();
  if (props.isCompany) getUnitList();
  if (props.isNumberSource) getCombList();
  gettimeSlotsList();
});
</script>

<style lang="scss" scoped>
/* 全局号源容器 */
.global-number-container {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow: hidden;
  background: white;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgb(0 0 0 / 10%);
}
.control-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}
.date-navigation,
.date-picker,
.actions {
  display: flex;
  gap: 10px;
  align-items: center;
}
.month-indicator {
  padding: 10px;
  margin: 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: #3a62d7;
  text-align: center;
  background: #f0f7ff;
  border-radius: 8px;
}
.legend {
  display: flex;
  gap: 15px;
  margin-left: auto;
}
.legend-item {
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 14px;
}
.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  &.holiday {
    background-color: #ffccc7;
  }
  &.today {
    background-color: #e6f7ff;
  }
  &.closed {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    color: #ff7a45;
    background-color: #ffe7ba;
  }
}

/* 滚动容器样式修复 */
.scroll-container {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-bottom: 20px;
  overflow: hidden auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  /* 修复右侧边框 */
  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    z-index: 5;
    width: 1px;
    height: 100%;
    content: "";
    background: #ebeef5;
  }
}

/* 表格头部样式 */
.table-header {
  //   position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  width: 100%;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}
.fixed-label {
  position: sticky;
  left: 0;
  z-index: 11;
  box-sizing: border-box;
  flex-shrink: 0;
  width: 140px;
  padding: 12px 16px;
  font-weight: bold;
  text-align: center;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
}
.dates-container {
  display: flex;
  flex-wrap: nowrap;
  width: calc(100% - 140px);
}
.date-header {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  max-width: calc(100% / 15);
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  &:last-child {
    border-right: none;
  }
}

/* 表格主体样式 */
.table-body {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
}
.data-row {
  display: flex;
  flex-grow: 1;
  min-width: min-content;
  background: white;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
  &:nth-child(odd) {
    background-color: #fdfdff;
  }
  &:hover {
    .row-label,
    .data-cell {
      background-color: #f0f5ff;
      &.holiday-cell,
      &.today {
        background-color: inherit;
      }
    }
  }
}
.row-label {
  position: sticky;
  left: 0;
  z-index: 10;
  box-sizing: border-box;
  flex-shrink: 0;
  align-content: center;
  width: 140px;
  padding: 14px 16px;
  font-weight: 500;
  text-align: center;
  background: inherit;
  border-right: 1px solid #e9ecef;
}
.row-data {
  display: flex;
  flex-wrap: nowrap;
  width: calc(100% - 140px);
  min-width: min-content;
}
.tow-data-row {
  flex: 0 0 auto;
  width: calc((100% / var(--days-per-row, 15)));
  max-width: calc(100% / 15);
}
.data-cell {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  max-width: calc(100% / 15);
  font-size: 14px;
  text-align: center;
  border-right: 1px solid #e9ecef;
  &.today {
    background-color: #e6f7ff !important;
    border: 2px solid #91d5ff;
  }
  &:last-child {
    border-right: none;
  }
  &:hover {
    z-index: 2;
    background-color: #e0ebff !important;
    box-shadow: 0 0 0 1px #c6e2ff;
  }
}
.holiday-cell {
  font-weight: 500;
  color: #e74c3c !important;
}
.today-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 12px;
  color: white;
  background-color: #1890ff;
  border-radius: 50%;
}
.status-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 12px;
  border-radius: 50%;
  &.closed {
    color: #ff7a45;
    background-color: #ffe7ba;
  }
}

/* 滚动条样式 */
.scroll-container::-webkit-scrollbar {
  height: 8px;
}
.scroll-container::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 4px;
}
.scroll-container::-webkit-scrollbar-thumb {
  background-color: #a0aec0;
  border-radius: 4px;
}
.form-box {
  padding: 20px 15px;
}
.data-select {
  display: flex;
  align-items: center;
  label {
    white-space: nowrap;
  }
}
</style>
