/*
 * @Author: Reaper
 * @Date: 2025-05-14 17:51:37
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-05-21 18:48:02
 * @Description: 请填写简介
 */
/**
 * @description 单位管理接口
 */
import { CompanyMgt } from "@/api/interface";
import { moduleRequest } from "@/api/request";
import { ResPage, ReqPage } from "@/api";
const http = moduleRequest("/admin/company/");

const companyMgtApi = {
  /** 单位分页 */
  page(params: CompanyMgt.Page) {
    return http.post<ResPage<CompanyMgt.Company>>("getcompanylist", params);
  },
  /** 同步单位 */
  syncCompany() {
    return http.post<boolean>("syncCompany", {});
  }
};

export { companyMgtApi };
