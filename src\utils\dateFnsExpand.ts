import { parse, format, isValid } from "date-fns";

// 定义支持的输出格式类型
type DateFormat = "yyyy-MM-dd" | "yyyy/MM/dd" | "MM-dd-yyyy" | "dd/MM/yyyy" | "MMMM do, yyyy" | "yyyy年MM月dd日";

// 定义返回结果的接口
interface DateFormatResult {
  original: string;
  formatted: string | null;
  valid: boolean;
  message: string;
  dateObject: Date | null;
}

// 定义错误类型
type DateValidationError = "INVALID_LENGTH" | "NOT_NUMERIC" | "INVALID_DATE" | "YEAR_OUT_OF_RANGE" | "MONTH_OUT_OF_RANGE";

/**
 * 格式化日期字符串并校验有效性
 * @param input - 8位数字日期字符串(如:19970518)
 * @param outputFormat - 输出格式(默认'yyyy-MM-dd')
 * @param options - 解析选项
 * @returns 格式化结果对象
 */
function formatAndValidateDate(input: string, outputFormat: DateFormat = "yyyy-MM-dd"): DateFormatResult {
  // 基本校验
  const validationError = validateDateString(input);
  if (validationError) {
    return createErrorResponse(input, validationError);
  }

  // 解析日期
  const date = parse(input, "yyyyMMdd", new Date());

  // 校验日期有效性
  if (!isValid(date)) {
    return createErrorResponse(input, "INVALID_DATE");
  }

  // 格式化日期
  const formattedDate = format(date, outputFormat);

  return {
    original: input,
    formatted: formattedDate,
    valid: true,
    message: "日期有效",
    dateObject: date
  };
}

/**
 * 校验日期字符串基本格式
 */
function validateDateString(input: string): DateValidationError | null {
  // 校验长度
  if (input.length !== 8) {
    return "INVALID_LENGTH";
  }

  // 校验是否为纯数字
  if (!/^\d+$/.test(input)) {
    return "NOT_NUMERIC";
  }

  const year = parseInt(input.substring(0, 4));
  const month = parseInt(input.substring(4, 6));

  // 校验年份范围(1900-当前年份+5)
  const currentYear = new Date().getFullYear();
  if (year < 1900 || year > currentYear + 5) {
    return "YEAR_OUT_OF_RANGE";
  }

  // 校验月份
  if (month < 1 || month > 12) {
    return "MONTH_OUT_OF_RANGE";
  }

  return null;
}

/**
 * 创建错误响应
 */
function createErrorResponse(input: string, errorType: DateValidationError): DateFormatResult {
  const errorMessages: Record<DateValidationError, string> = {
    INVALID_LENGTH: "输入必须是8位字符",
    NOT_NUMERIC: "输入必须为数字",
    INVALID_DATE: "无效日期",
    YEAR_OUT_OF_RANGE: "年份超出有效范围",
    MONTH_OUT_OF_RANGE: "月份无效"
  };

  return {
    original: input,
    formatted: null,
    valid: false,
    message: errorMessages[errorType],
    dateObject: null
  };
}

// 使用示例
const result1 = formatAndValidateDate("19970518", "yyyy-MM-dd");
console.log(result1);

const result2 = formatAndValidateDate("19970518", "yyyy/MM/dd");
console.log(result2);

const result3 = formatAndValidateDate("19970230", "yyyy-MM-dd");
console.log(result3);

export { formatAndValidateDate };
