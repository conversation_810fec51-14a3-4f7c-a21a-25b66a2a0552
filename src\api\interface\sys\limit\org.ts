/**
 * @description 组织机构管理接口
 */

import { ReqPage } from "@/api";

export namespace SysOrg {
  /** 分页查询 */
  export interface Page extends ReqPage {
    /** 状态 */
    status?: string;
  }

  /** 机构信息 */
  export interface SysOrgInfo {
    id: number | string;
    /** 机构名称 */
    orgName: string;
    /** 机构编码 */
    orgCode: string;
    /** 院区名称 */
    areaName: string;
    /** 院区编码 */
    areaCode: string;
    /** 头像 */
    avatar: string;
    /**信息描述 */
    describe: string;
    /**地址 */
    address: string;
    /**电话标签 */
    telTag: string;
    /** 状态 */
    status?: string;
  }
}
