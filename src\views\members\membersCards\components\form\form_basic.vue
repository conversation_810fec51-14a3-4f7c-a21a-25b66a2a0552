<!-- 基本信息-->
<template>
  <div>
    <el-tab-pane label="基础信息" name="basic">
      <el-row :gutter="16">
        <el-col :span="12">
          <s-form-item label="姓名" prop="Name">
            <s-input v-model="userInfo.name"></s-input>
          </s-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <s-form-item label="手机号" prop="tel">
            <s-input v-model="userInfo.tel" disabled="true"></s-input>
          </s-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <s-form-item label="证件类型" prop="cardtype">
            <s-input v-model="userInfo.cardType"></s-input>
          </s-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <s-form-item label="证件号" prop="idCard">
            <s-input v-model="userInfo.idCard"></s-input>
          </s-form-item>
        </el-col> -->
        <el-col :span="12">
          <s-form-item label="证件号" prop="cardNo">
            <s-input v-model="userInfo.cardNo" disabled="true"></s-input>
          </s-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="16">
        <el-col :span="12">
          <s-form-item label="状态" prop="status">
            <s-radio-group v-model="userInfo.status" :options="statusOptions" button />
          </s-form-item>
        </el-col>
      </el-row> -->
    </el-tab-pane>
  </div>
</template>

<script setup lang="ts">
import { SysMembers } from "@/api";

// props
interface FormProps {
  modelValue: Partial<SysMembers.Members_lower>;
}
const emit = defineEmits(["update:modelValue"]); //定义emit
const props = defineProps<FormProps>(); //定义props
// 用户信息
const userInfo = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});
// console.log("userInfo", userInfo);
// onMounted(() => {
//   // 初始化
// });
</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  width: 100% !important;
}
:deep(.el-date-editor.el-input) {
  width: 92% !important;
}
</style>
