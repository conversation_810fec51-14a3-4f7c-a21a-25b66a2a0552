<template>
  <div>
    <form-container v-model="visible" :title="`${combinationMutuallyExclusiveProps.opt}互斥关系`" form-size="90%" @close="onClose">
      <div class="form-container-body">
        <el-form
          class="form"
          ref="combinationMutuallyExclusiveFormRef"
          :rules="rules"
          :disabled="combinationMutuallyExclusiveProps.disabled"
          :model="combinationMutuallyExclusiveProps.record"
          :hide-required-asterisk="combinationMutuallyExclusiveProps.disabled"
          label-width="auto"
          label-suffix=" :"
        >
          <s-form-item label="互斥编码" prop="mutexCode">
            <s-input v-model="combinationMutuallyExclusiveProps.record.mutexCode" placeholder="请输入互斥编码" disabled></s-input>
          </s-form-item>
          <s-form-item label="互斥名称" prop="mutexName">
            <s-input v-model="combinationMutuallyExclusiveProps.record.mutexName" placeholder="请输入互斥名称"></s-input>
          </s-form-item>
        </el-form>
        <div class="table-box">
          <div class="table-container">
            <el-input v-model="searchCombination" placeholder="请输入组合名称">
              <template #prepend>
                <el-select v-model="searchCombinationOption" placeholder="" style="width: 200px" @change="onSearchCombination">
                  <el-option :label="item.label" :value="item.value" v-for="(item, index) in projectClsOptionsMap" :key="index" />
                </el-select>
              </template>
              <template #append>
                <el-button :icon="Search" @click="onSearchCombination" />
              </template>
            </el-input>
            <el-table :data="filteredCombination" style="width: 100%" height="100%" border>
              <el-table-column prop="clsName" label="项目分类" />
              <el-table-column prop="combCode" label="组合编码" />
              <el-table-column prop="combName" label="组合名称" />
              <el-table-column prop="price" label="价格" />
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button link type="primary" @click="onAddCombination(scope.row)">添加</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="table-container">
            <div class="selected-header">
              <span class="selected-title">已选择组合</span>
              <span class="selected-count">共 {{ selectedCombination.length }} 项</span>
              <span class="selected-price">总价: {{ totalPrice.toFixed(2) }}元</span>
            </div>
            <el-table :data="selectedCombination" style="width: 100%" height="100%" border>
              <el-table-column prop="clsName" label="项目分类" />
              <el-table-column prop="combCode" label="组合编码" />
              <el-table-column prop="combName" label="组合名称" />
              <el-table-column prop="price" label="价格" />
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button link type="danger" @click="onRemoveCombination(scope.row)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!combinationMutuallyExclusiveProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { CombinationMutuallyExclusive, combinationMutuallyExclusiveApi, combinationManagementApi, CombinationManagement } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";
import { Search } from "@element-plus/icons-vue";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
// 项目分类选项
const projectClsOptions = dictStore.getDictList(SysDictEnum.PROJECT_CLS);
// 筛选后的组合数据
const filteredCombination = ref<CombinationManagement.CombinationInfo[]>([]);
// 组合数据
const combinationData = ref<CombinationManagement.CombinationInfo[]>([]);
// 已选择组合数据
const selectedCombination = ref<CombinationManagement.CombinationInfo[]>([]);
// 搜索组合名称
const searchCombination = ref("");
// 搜索项目分类选项
const searchCombinationOption = ref("-1");
// 表单参数
const combinationMutuallyExclusiveProps = reactive<FormProps.Base<CombinationMutuallyExclusive.CombinationMutuallyExclusiveInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  // mutexCode: [required("请输入互斥编码")],
  mutexName: [required("请输入互斥名称")]
});

// 计算总价
const totalPrice = computed(() => {
  const total = selectedCombination.value.reduce((sum, item) => sum + item.price, 0);
  return Number(total.toFixed(2));
});
/** 计算项目分类选项 */
const projectClsOptionsMap = computed(() => {
  return [{ value: "-1", label: "全部项目分类" }, ...projectClsOptions];
});

/**
 * 打开表单
 * @param props 表单参数
 */
async function onOpen(props: FormProps.Base<CombinationMutuallyExclusive.CombinationMutuallyExclusiveInfo>) {
  Object.assign(combinationMutuallyExclusiveProps, props); //合并参数
  await getCombsByItemCls();
  if (props.opt == FormOptEnum.ADD) {
    combinationMutuallyExclusiveFormRef.value?.resetFields();
    selectedCombination.value = [];
    //如果是新增,设置默认值
    combinationMutuallyExclusiveProps.record = {
      mutexCode: "",
      mutexName: ""
    };
  }
  visible.value = true; //显示表单
  if (props.opt == FormOptEnum.EDIT) {
    combinationMutuallyExclusiveProps.record = JSON.parse(JSON.stringify(props.record));
    combinationMutuallyExclusiveApi.detail({ mutexCode: props.record.mutexCode! }).then(res => {
      const combCode = res.data.flatMap(item => item.combCode);
      selectedCombination.value = combinationData.value.filter(item => combCode.includes(item.combCode));
    });
  }
}

// 提交数据（新增/编辑）
const combinationMutuallyExclusiveFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  combinationMutuallyExclusiveFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    const params = selectedCombination.value.map(item => ({
      mutexCode: combinationMutuallyExclusiveProps.record.mutexCode,
      mutexName: combinationMutuallyExclusiveProps.record.mutexName,
      combCode: item.combCode,
      combName: item.combName
    }));
    //提交表单
    await combinationMutuallyExclusiveApi
      .submitForm(params)
      .then(() => {
        ElMessage.success("保存成功");
        combinationMutuallyExclusiveProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

/** 搜索组合 */
function onSearchCombination() {
  // 先根据分类过滤基础数据
  const baseData =
    searchCombinationOption.value === "-1"
      ? combinationData.value
      : combinationData.value.filter(item => item.clsCode === searchCombinationOption.value);

  // 再根据搜索词过滤组合名称
  filteredCombination.value = baseData.filter(comb => comb.combName.includes(searchCombination.value));
}
/** 添加组合 */
function onAddCombination(row: CombinationManagement.CombinationInfo) {
  if (selectedCombination.value.some(item => item.combCode === row.combCode)) {
    ElMessage.warning("已存在该组合");
    return;
  }
  const addPackageCode = filteredCombination.value.some(item => item.combCode === row.combCode);
  if (addPackageCode) {
    selectedCombination.value.push(row);
  }
}
/**
 * 移除组合
 * @param row 组合数据
 */
function onRemoveCombination(row: CombinationManagement.CombinationInfo) {
  const index = selectedCombination.value.findIndex(item => item.combCode === row.combCode);
  if (index !== -1) {
    selectedCombination.value.splice(index, 1);
  }
}
/** 获取项目组合 */
async function getCombsByItemCls() {
  const { data } = await combinationManagementApi.getCombination();
  filteredCombination.value = data;
  combinationData.value = data;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container-body {
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: 1fr;
  gap: 15px;
  margin-top: 20px;
  .form {
    display: flex;
    gap: 15px;
    width: 60%;
  }
  .table-box {
    display: grid;
    grid-template-rows: 60vh;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    width: 100%;
  }
  .table-container {
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
    .selected-header {
      display: grid;
      grid-template-columns: 86px 1fr 1fr;
      align-items: center;
      padding: 6px 8px;
      font-weight: bold;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
    .combination-container {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px;
      overflow: auto;
      border-radius: 4px;
      outline: 1px solid var(--el-border-color);
      .combination-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .combination-title {
        font-weight: bold;
      }
      .combination-checkbox-group {
        margin-left: 20px;
      }
      .combination-checkbox {
        margin-bottom: 20px;
      }
    }
    .selected-count {
      font-weight: normal;
    }
    .selected-price {
      color: var(--el-color-danger);
      text-align: right;
    }
  }
}
:deep(.el-dialog__body) {
  margin-top: 0;
}
:deep(.el-drawer__body) {
  margin-top: -20px;
}
:deep(.el-input-number) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding: 0;
  .el-input__inner {
    font-size: 1.1em;
    text-align: right;
  }
}
</style>
