/**
 * @Description:登录的方法
 */
import { SysOrg } from "@/api/interface";
export namespace Login {
  /** 模块信息 */
  export type ModuleInfo = {
    id: number | string;
    title: string;
    code: string;
    icon: string;
    description: string;
  };

  /**
   * 验证码
   */
  interface ValidCode {
    /**  验证码 */
    validCode: string;
    /** 验证码请求号 */
    validCodeReqNo: string;
  }

  /**
   * 账号密码登录表单
   */
  export interface LoginForm extends ValidCode {
    account: string;
    password: string;
    Device: string;
    orgKey: string;
  }

  /**
   * 注销表单
   */
  export interface Logout {
    token: string;
  }

  // 登录返回
  export interface Login {
    /** token */
    token: string;
    /** 默认模块 */
    defaultModule: string;
    /** 模块列表 */
    moduleList: ModuleInfo[];
    /** 机构列表 */
    orgList: SysOrg.SysOrgInfo[];
  }

  /**
   * 验证码返回
   */
  export interface ReqValidCode {
    /** 验证码 */
    validCodeBase64: string;
    /** 验证码请求号 */
    validCodeReqNo: string;
  }

  /** 用户信息 */
  export interface LoginUserInfo {
    /** 用户id */
    id: string | number;
    /** 用户名 */
    account: string;
    /** 用户姓名 */
    name: string;
    /** 用户头像 */
    avatar: string;
    /** 电话号码 */
    phone: string;
    /** 邮箱 */
    email: string;
    /** 默认模块 */
    defaultModule: string;
    /** 模块列表 */
    moduleList: ModuleInfo[];
    /** 按钮码集合 */
    buttonCodeList: string[];
    /** 权限码集合 */
    permissionCodeList: string[];
    /** 角色码集合 */
    roleCodeList: string[];
    orgList: SysOrg.SysOrgInfo[];
  }
}
