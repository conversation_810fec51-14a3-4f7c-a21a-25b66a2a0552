<script setup lang="ts">
import UnitLogin from "./form/index.vue";
defineOptions({
  name: "Phone"
});
const emits = defineEmits(["update:phoneStatus"]);
const props = defineProps({
   unitInfo: {
    type: Object,
    required: true
  },
  /* 是否显示 */
  phoneStatus: {
    type: Boolean,
    required: true,
    default() {
      return false;
    }
  },
  /* 标题 */
  phoneTitle: {
    type: String,
    required: true,
    default() {
      return "";
    }
  }
});

/* 关闭回调 */
function phoneClose() {
  emits("update:phoneStatus", false);
}

/* 手机时间 */
function showData() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const currentTime = `${hours < 10 ? "0" + hours : hours}:${minutes}`;
  return currentTime;
}
</script>

<template>
  <div>
    <div class="look_box">
      <div class="phone">
        <div class="phone_shadow">
          <div class="nav">
            <div style="padding-top: 5px; padding-left: 5px; font-size: 15px">
              {{ showData() }}
            </div>
            <div class="icons">
              <span style="margin-right: 5px; font-size: 22px">...</span>
              <span style="margin-right: 7px">
                <svg
                  t="1701753286800"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="1539"
                  width="18"
                  height="18"
                >
                  <path
                    d="M42.666667 384l85.333333 85.333333c212.053333-212.053333 555.946667-212.053333 768 0l85.333333-85.333333C722.133333 124.8 301.866667 124.8 42.666667 384z m341.333333 341.333333l128 128 128-128c-70.613333-70.613333-185.386667-70.613333-256 0z m-170.666667-170.666666l85.333334 85.333333c117.76-117.76 308.906667-117.76 426.666666 0l85.333334-85.333333c-164.906667-164.906667-432.426667-164.906667-597.333334 0z"
                    p-id="1540"
                    fill="#2d4247"
                  />
                </svg>
              </span>
              <span>
                <svg
                  t="1701754878961"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="6536"
                  width="23"
                  height="23"
                >
                  <path
                    d="M144.700101 684.994006l535.580045 0L680.280146 359.237781 144.700101 359.237781 144.700101 684.994006 144.700101 684.994006zM918.373823 440.680675l0-81.442894c0-44.791136-36.649711-81.437777-81.437777-81.437777l-692.235944 0c-44.791136 0-81.437777 36.646642-81.437777 81.437777L63.262324 684.994006c0 44.791136 36.646642 81.442894 81.437777 81.442894l692.235944 0c44.788066 0 81.437777-36.650735 81.437777-81.442894l0-81.437777c22.396079 0 40.7194-18.322297 40.7194-40.7194l0-81.436754C959.093223 459.003995 940.769902 440.680675 918.373823 440.680675L918.373823 440.680675zM877.655446 481.400075l0 81.436754L877.655446 684.994006c0 22.395056-18.323321 40.718377-40.7194 40.718377l-692.235944 0c-22.396079 0-40.7194-18.323321-40.7194-40.718377L103.980701 359.237781c0-22.396079 18.323321-40.7194 40.7194-40.7194l692.235944 0c22.396079 0 40.7194 18.323321 40.7194 40.7194L877.655446 481.400075 877.655446 481.400075zM877.655446 481.400075"
                    fill="#2d4247"
                    p-id="6537"
                  />
                </svg>
              </span>
            </div>
          </div>
          <div class="nav_s" style="background-color: #ffffff" v-if="false">
            <div class="span_title">{{ props.phoneTitle }}</div>
          </div>
          <!-- 内容区域 -->
          <div class="phone_conts" style="background-color: #f3f4f6">
            <UnitLogin  />
          </div>
          <div class="bottom_bar" />
        </div>
        <div class="volume" />
        <div class="bang">
          <div class="yuan" />
        </div>
        <div class="power1" />
        <div class="power2" />
        <div class="power3" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 手机样式 */
:deep(.look_box .el-dialog__header) {
  display: none;
}
:deep(.look_box .el-dialog) {
  border-radius: 0 !important;
}
:deep(.look_box .el-dialog__body) {
  padding: 0 !important;
}
/* stylelint-disable-next-line no-duplicate-selectors */
:deep(.look_box .el-dialog) {
  background: transparent !important;
  box-shadow: none;
}
/* stylelint-disable-next-line no-duplicate-selectors */
:deep(.look_box .el-dialog__body) {
  border: none !important;
}
.html_conts img {
  width: 100%;
  height: auto;
  padding: 5px 0;
  vertical-align: bottom;
}
.look_box {
  .look_box_cont {
    height: 500px;
  }
}
.phone {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  width: 332px;
  height: 686px;
  border: 15px solid #000000;
  border-radius: 55px;
}
.phone_shadow {
  position: absolute;
  top: -9px;
  left: -9px;
  z-index: 8;
  box-sizing: border-box;
  width: 320px;
  height: 673px;
  overflow: hidden;
  content: "";
  background: #ffffff;
  border: 4px solid #000000;
  border-radius: 48px;
  box-shadow: 0 0 24px #ffffff;
  .nav {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding-right: 19px;
    padding-left: 35px;
    margin-top: 4px;
    font-size: 16px;
    color: #333333;
    .icons {
      display: flex;
      align-items: center;
    }
  }
  .nav_s {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    padding: 0 10px;
    font-size: 16px;
    background-color: #f4f4f5;
    border-bottom: 1px solid #ececec;
    .span_title {
      position: absolute;
      top: 50%;
      left: 50%;
      display: -webkit-box;
      overflow: hidden;
      font-size: 16px;
      color: #000000;
      text-overflow: ellipsis;
      transform: translate(-50%, -50%);
      -webkit-box-orient: vertical;
    }
    .left {
      font-size: 20px;
      color: #000000;
    }
    .right {
      color: #83878d;
      .one {
        margin-right: 5px;
      }
    }
  }
  .phone_conts {
    box-sizing: border-box;
    color: #000000;
    .titles {
      font-size: 16px;
      font-weight: 700;
      line-height: 30px;
    }
    .data {
      line-height: 26px;
      color: #666769;
      border-bottom: 1px dashed #c6c6c6;
    }
    .html_conts {
      margin-top: 20px;
      line-height: 30px;
      color: #666769;
      word-break: break-all;
    }
  }
  .bottom_bar {
    position: absolute;
    bottom: 5px;
    left: 50%;
    width: 120px;
    height: 4px;
    background-color: #2d4247;
    border-radius: 5px;
    transform: translateX(-50%);
  }
}
.phone_conts::-webkit-scrollbar {
  display: none;
}
.volume {
  position: absolute;
  top: 172px;
  right: -18px;
  z-index: -1;
  width: 5px;
  height: 100px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 0 20px 20px 0;
}
.power1 {
  position: absolute;
  top: 100px;
  left: -19px;
  z-index: -1;
  width: 5px;
  height: 35px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 20px 0 0 20px;
}
.power2 {
  position: absolute;
  top: 158px;
  left: -19px;
  z-index: -1;
  width: 5px;
  height: 58px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 20px 0 0 20px;
}
.power3 {
  position: absolute;
  top: 232px;
  left: -19px;
  z-index: -1;
  width: 5px;
  height: 58px;
  background: linear-gradient(#8c8c8c, #000000 9%, #222222, #000000 94%, #353535 100%);
  border: #000000 1px solid;
  border-radius: 20px 0 0 20px;
}
.receiver {
  position: absolute;
  top: 4px;
  left: 50%;
  z-index: 9;
  width: 50px;
  height: 8px;
  background: #000000;
  border: #484848 1px solid;
  border-radius: 20px;
  transform: translateX(-50%);
}
.bang {
  position: absolute;
  top: 4px;
  left: 50%;
  z-index: 9;
  width: 93px;
  height: 26px;
  background-color: #000000;
  border-radius: 13px;
  transform: translateX(-50%);
}
.yuan {
  position: absolute;
  top: 7px;
  right: 9px;
  box-sizing: border-box;
  width: 12px;
  height: 12px;
  background-color: #0f2563;
  border-radius: 50%;
}
.look_my_phone .phone_conts {
  padding: 0 !important;
  overflow: hidden !important;
}
</style>
