import { PersistedStateOptions, StorageLike } from "pinia-plugin-persistedstate";
import SecureLS from "secure-ls";

const ls = new SecureLS({
  isCompression: false, //不压缩 //自定义秘钥
  encryptionSecret: "krinfosoft" // 这个地方使用自己自定义的加密字符串
});

export const SelfStorage: StorageLike = {
  setItem(key, value) {
    ls.set(key, value);
  },
  getItem(key) {
    return ls.get(key);
  }
};

/**
 * @description pinia 持久化参数配置
 * @param {String} key 存储到持久化的 name
 * @param {Array} paths 需要持久化的 state name
 * @return persist
 * */
const piniaPersistConfig = (key: string, paths?: string[]) => {
  const persist: PersistedStateOptions = {
    key,
    storage: SelfStorage, //localStorage
    // storage: sessionStorage,
    paths
  };
  return persist;
};

export default piniaPersistConfig;
