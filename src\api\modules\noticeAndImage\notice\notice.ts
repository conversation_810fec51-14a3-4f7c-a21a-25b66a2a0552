import { moduleRequest } from "@/api/request";
import { ResPage, sysNotice, ReqId, ResultData } from "@/api";
const http = moduleRequest("/admin/NoticeAndImage/");

const noticeApi = {
  /** 分页 */
  page(params: sysNotice.Page) {
    return http.post<{}>("GetAllNotices", params);
  },

  /** 编辑体检须知:更新、新增 */
  EditNotices(params: {}, edit: boolean = true) {
    return http.post<Boolean>(edit ? "UpdateNoticeContentById" : "AddNotices", params);
  },
  /**更新轮播图状态 */
  UpdateNoticeStatus(params: {}) {
    return http.post<Boolean>("UpdateNoticeStatus", params);
  }
};

export { noticeApi };
