/**
 * @description 客户管理接口
 */

import { ReqPage } from "@/api";

export namespace SysMembers {
  /** 客户分页查询 */
  export interface Page extends ReqPage {
    name: string;
    wId: string;
    tel: string;
    cardNo: string;
  }

  /** 客户信息 */
  export interface Members {
    Id: string;
    Name: string;
    WId: string;
    Tel: string;
    CardNo: string;
    createDate: string;

    // createUserId: "";
    // createUserName: "";
    // extJson: "";
    // modifyUserName: "";
    // modifyUserId: "";
    // modifyDate: "";
  }
  export interface Members_lower {
    id: string;
    name: string;
    // typeCard: string;
    idCard: string;
    tel: string;
    cardNo: string;
    createDate: string;
    wId: string;
  }
}
