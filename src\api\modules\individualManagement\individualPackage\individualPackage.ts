/*
 * @Author: Reaper
 * @Date: 2025-05-22 18:58:04
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-26 09:46:56
 * @Description: 请填写简介
 */
/**
 * @description 个检套餐接口
 */
import { IndividualPackage, AddPackage } from "@/api/interface";
import { moduleRequest } from "@/api/request";
import { ResPage, ReqPage } from "@/api";
const http = moduleRequest("/admin/basiccode/");
const https = moduleRequest("/admin/syncbusiness/");

const individualPackageApi = {
  /** 个检套餐分页 */
  page(params: IndividualPackage.Page) {
    return http.post<IndividualPackage.IndividualPackageInfo[]>("getcodecluster", params);
  },
  /** 获取组合项目 */
  getCombsByItemCls() {
    return http.get<IndividualPackage.CombsByItemCls[]>("getcombsbyitemclsgroup");
  },
  /** 获取套餐详情 */
  getClusterByComb(params: {}) {
    return http.post<IndividualPackage.CombinationData[]>("getmapclustercomb", params);
  },
  /** 新增/修改套餐 */
  submitForm(params: {}) {
    return http.post("saveclusterandmapcombs", params);
  },
  /** 删除套餐 */
  delete(params: {}) {
    return http.post("deletecodecluster", params);
  },
  /** 获取套餐外的项目 */
  getClusterExtraComb(params: {}) {
    return http.post<IndividualPackage.CombinationData[]>("getmapclusterextracomb", params);
  },
  /** 保存套餐外的项目 */
  saveClusterExtraComb(params: {}) {
    return http.post("savemapclusterextracomb", params);
  },
  /** 获取套餐加项包 */
  getClusterAddPackage(params: {}) {
    return http.post<AddPackage.AddPackageInfo[]>("getclusteraddpackage", params);
  },
  /** 保存套餐加项包 */
  saveClusterAddPackage(params: {}) {
    return http.post("saveclusteraddpackage", params);
  },
  /** 同步套餐及对应组合 */
  SyncClusterAndCombs() {
    return https.post<boolean>("SyncClusterAndCombs");
  }
};

export { individualPackageApi };
