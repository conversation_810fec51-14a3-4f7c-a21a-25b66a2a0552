
interface Pagination {
  currentPage: number;
  pageSize: number;
  total: number;
}

const defaultPagination: Pagination = {
  currentPage: 1,
  pageSize: 20,
  total: 0
};

export const usePagination = () => {
  const pagination = ref<Pagination>(deepClone(defaultPagination));

  function handleSizeChange(size: number) {
    pagination.value.pageSize = size;
    pagination.value.currentPage = 1;
  }

  function handleCurrentChange(page: number) {
    pagination.value.currentPage = page;
  }
  function reset(){
    deepClone(defaultPagination);
  }

  return {
    pagination,

    reset,
    handleSizeChange,
    handleCurrentChange
  };
};
function deepClone(obj: any) {
  return JSON.parse(JSON.stringify(obj));
}