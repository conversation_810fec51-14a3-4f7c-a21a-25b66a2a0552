/**
 * @description 组合包含接口
 */
import { CombinationEmbody } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/admin/basiccode/");

const combinationEmbodyApi = {
  /** 组合包含关系查询 */
  page() {
    return http.post<CombinationEmbody.CombinationEmbodyInfo[]>("getcombcontain", {});
  },
  /** 删除组合包含关系 */
  delete(params: string[]) {
    return http.post<boolean>("deletecombcontain", params);
  },
  /** 获取组合包含明细 */
  detail(params: {}) {
    return http.post<CombinationEmbody.CombinationEmbodyDetailInfo[]>("getcombcontaindetail", {}, { params });
  },
  /** 新增/修改包含组合 */
  submitForm(params: {}) {
    return http.post("savecombcontaindetail", params);
  }
};

export { combinationEmbodyApi };
