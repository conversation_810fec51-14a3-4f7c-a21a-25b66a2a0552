<template>
  <div class="unit-list-container">
    <el-row :gutter="20">
      <!-- 左侧：列表 -->
      <el-col :span="12">
        <ProTable ref="proTable" title="列表" :columns="columns" :request-api="realTimeInfoApi.page" :init-param="initParam">
          <!-- 表格 header 按钮 -->
          <template #tableHeader="scope">
            <s-button @click="onOpen(FormOptEnum.ADD)" />
            <s-button
              type="danger"
              v-auth="userButtonCode.操作角色"
              plain
              :opt="FormOptEnum.DELETE"
              :disabled="!scope.isSelected"
              @click="onDelete(scope.selectedListIds, '删除所选资讯')"
            />
          </template>

          <!-- 状态 -->
          <template #status="scope">
            <el-switch
              v-auth="userButtonCode.操作用户"
              :model-value="scope.row.status === CommonStatusEnum.ENABLE"
              :loading="switchLoading"
              @change="editStatus(scope.row)"
            />
          </template>

          <!-- 图片 -->
          <template #diagramUrl="scope">
            <el-avatar v-if="scope.row.diagramUrl" :src="url + scope.row.diagramUrl" :size="40" shape="square" />
          </template>

          <!-- 操作 -->
          <template #operation="scope">
            <el-space>
              <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
              <s-button link @click="handleUnitSelect(scope.row)">
                <span style="font-weight: bold; color: red" v-if="scope.row.id === selectValue.id">*</span>
                文本
                <span style="font-weight: bold; color: red" v-if="scope.row.id === selectValue.id">*</span>
              </s-button>
              <!-- <el-button type="primary">文本</el-button> -->
            </el-space>
          </template>
        </ProTable>
        <Form ref="formRef" />
      </el-col>
      <!-- <el-col :span="2"></el-col> -->
      <!-- 右侧： -->
      <el-col :span="12">
        <div v-if="true">
          <WangEditor v-model:value="content" height="400px" :key="editorKey" ref="wangEditor" :disabled="isDisabled" />
          <div style="display: flex; justify-content: center; margin: 5px 0">
            <el-button type="primary" round @click="EditNotices()" style="width: 10rem">保存</el-button>
          </div>
        </div>
      </el-col>

      <!-- <el-col :span="10">
        <PhoneStyle :phoneStatus="true" phone-title="xxx" />
      </el-col> -->
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { sysRealTimeInfo, realTimeInfoApi, userButtonCode } from "@/api";
import WangEditor from "@/components/WangEditor/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useDictStore } from "@/stores/modules";
import { SysDictEnum, FormOptEnum, CommonStatusEnum, DictCategoryEnum } from "@/enums";
import Form from "./form/form.vue";
// import { Edit } from '@element-plus/icons-vue'
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import { ElLoading } from "element-plus";

//上传地址
const url = import.meta.env.VITE_API_URL as string;

interface InitParam {
  sortField: string;
}
/**如果表格需要初始化请求参数 */
const initParam = reactive<InitParam>({
  sortField: ""
});
//获取字段
const dictStore = useDictStore(); //字典仓库
/**状态选项 */
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
/**表格配置项 */
const columns: ColumnProps<sysRealTimeInfo.realTimeInfoTable>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "title", label: "标题", width: 100, search: { el: "input" }, isShow: true },
  {
    prop: "status",
    label: "状态",
    width: 80,
    enum: statusOptions,
    search: { el: "tree-select" }
  },
  { prop: "diagramUrl", label: "缩列图", width: 180 },

  { prop: "createDate", label: "创建时间", width: 130 },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];
const proTable = ref<ProTableInstance>();
/**表单引用 */
const formRef = ref<InstanceType<typeof Form> | null>(null);
//文本设置
/**文本内容 */
const content = ref("");
/**禁用文本 */
const isDisabled = ref(true);
/**触发编辑器重载 */
const editorKey = ref(0);
const wangEditor = ref();
/**监视富文本，当文本状态变化时，编辑器重载 */
watch(
  () => isDisabled.value,
  newValue => {
    if (newValue !== undefined) {
      editorKey.value++; // 触发编辑器重建
    }
  }
);
/**选中的内容 */
const selectValue = ref<sysRealTimeInfo.selectValue>({
  id: "",
  content: ""
});
/**状态开关loading */
const switchLoading = ref(false);

/**打开新增表单 */
function onOpen(opt: FormOptEnum, record: {} | sysRealTimeInfo.realTimeInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}
/**删除 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(realTimeInfoApi.DeleteRealTimeInfo, { ids }, msg);
  RefreshTable();
}

// 初始化
// onMounted(() => {
//
// });

/**处理点击(编译文本) */
async function handleUnitSelect(row: any) {
  //判断编译是否保存
  if (selectValue.value.id != "" && selectValue.value.content != content.value) {
    ElMessageBox.confirm("您的编译尚未保存，是否放弃?", "温馨提示", {
      confirmButtonText: "放弃",
      cancelButtonText: "取消",
      type: "warning",
      draggable: true
    })
      .then(async () => {
        await selectValueEqualTo(row);
      })
      .catch(() => {});
  } else {
    await selectValueEqualTo(row);
  }
}
/****选中赋值****/
async function selectValueEqualTo(row: any) {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  // 先触发编辑器重建
  isDisabled.value = false;
  editorKey.value++;
  await nextTick(); // 确保DOM更新完成
  // 添加延迟确保编辑器实例化完成
  return new Promise<void>(resolve =>
    setTimeout(async () => {
      const { data } = await realTimeInfoApi.GetRealTimeContent({ kw: row.id, code: "" });
      selectValue.value.content = data;
      selectValue.value.id = row.id;
      content.value = data;
      loading.close();
      resolve();
    }, 100)
  );
}

/***保存数据 */
const saveBei = ref("");
//更新文本内容
async function EditNotices() {
  await realTimeInfoApi
    .UpdateRealTimeInfoContentById({
      id: selectValue.value.id,
      content: content.value
    })
    .then(res => {
      if (res.code == "200") {
        ElMessage.success("保存成功");
        selectValue.value.content = content.value;
        saveBei.value = JSON.stringify(selectValue.value);
        RefreshTable();
      } else {
        ElMessage.error("保存失败");
      }
    });
}
/**修改资讯状态 */
async function editStatus(row: sysRealTimeInfo.realTimeInfoStatus) {
  const status = row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE; // 状态取反
  switchLoading.value = true;
  realTimeInfoApi
    .UpdateRealTimeInfoStatus({
      id: row.id,
      status: status
    })
    .then(res => {
      if (res.code == "200") {
        ElMessage.success("修改成功");
        row.status = status;
      } else {
        ElMessage.error("修改失败");
      }
    })
    .catch(() => {
      ElMessage.error("请求异常");
    })
    .finally(() => {
      switchLoading.value = false;
    });
}
/**
 * 刷新表格
 */
async function RefreshTable() {
  await proTable.value?.refresh(); //刷新表格
  //刷新后重新定位
  if (saveBei.value) {
    selectValue.value = JSON.parse(saveBei.value);
  }
}
</script>

<style scoped lang="scss">
// .search-label {
//   margin: 0 10px;
//   font-weight: bold;
// }
.stickyTop {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #ffffff;
}
.stickyFooter {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  z-index: 1000;
  background-color: #ffffff;
}
.unit-list-container {
  cursor: pointer;
  .unit-card {
    height: 85vh;
  }
  .person-lists-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 80vh;
    .search-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;
      .header-actions {
        display: flex;
        gap: 10px;
        margin-left: auto;
      }
    }
    .person-card {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
      &.inactive-card {
        margin-bottom: 15px;
      }
      :deep(.el-card__body) {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: hidden;
      }
      .person-list {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: hidden;
        .el-table {
          flex: 1;
        }
      }
    }
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 16px;
      font-weight: bold;
    }
    .count-badge {
      padding: 2px 8px;
      font-size: 12px;
      color: white;
      background-color: #f56c6c;
      border-radius: 10px;
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }

  // 导入对话框样式
  // .import-dialog-content {
  //   min-height: 60%;

  //   .upload-area {
  //     display: flex;
  //     justify-content: center;
  //     padding: 20px;
  //   }

  //   .preview-area {
  //     .preview-header {
  //       display: flex;
  //       align-items: center;
  //       justify-content: space-between;
  //       margin-bottom: 15px;

  //       h3 {
  //         margin: 0;
  //       }

  //       .preview-actions {
  //         display: flex;
  //         gap: 10px;
  //       }
  //     }
  //   }
  // }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }
  ::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}
</style>
