/**
 * @description 认证模块
 */
import { defineStore } from "pinia";
import { getFlatMenuList, getShowMenuList, getAllBreadcrumbList } from "@/utils";
import { Login, UserCenter } from "@/api/interface";
import { loginApi, userCenterApi } from "@/api";
import { useUserStore } from "./user";
import { useTabsStore } from "./tabs";
import { useKeepAliveStore } from "./keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import { ElNotification } from "element-plus";
import { getTimeState, getCurrentDateTime } from "@/utils";
import router from "@/routers";

const name = "base-auth"; // 定义模块名称

/* AuthState */
export interface AuthState {
  /** 登录的加载状态 */
  loginLoading: boolean;
  /** 是否开启选择模块 */
  showChooseModule: boolean;
  /** 模块列表 */
  moduleList: Login.ModuleInfo[];
  /** 菜单权限列表 */
  authButtonList: string[];
  /** 菜单权限列表 */
  authMenuList: Menu.MenuOptions[];
}

/** 认证模块 */
export const useAuthStore = defineStore({
  id: name,
  state: (): AuthState => ({
    loginLoading: false,
    showChooseModule: false,
    moduleList: [],
    authButtonList: [],
    authMenuList: []
  }),
  getters: {
    // 按钮权限列表
    authButtonListGet: state => state.authButtonList,
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: state => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: state => getShowMenuList(state.authMenuList),
    // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
    flatMenuListGet: state => getFlatMenuList(state.authMenuList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList)
  },
  actions: {
    /** 设置模块列表 */
    SetModuleList(moduleList: Login.ModuleInfo[]) {
      this.moduleList = moduleList;
    },
    /** 选择应用模块 */
    async chooseModule(config: UserCenter.ResModuleDefault) {
      const userStore = useUserStore();
      userStore.setModule(config.id); //存储选择的模块
      await userCenterApi.setDefaultModule(config); //设置默认模块
      this.showChooseModule = false; //选择模块状态为关闭
    },
    /** 获取按钮列表 */
    async getAuthButtonList() {
      const userStore = useUserStore();
      const { userInfo } = userStore;
      this.authButtonList = userInfo?.buttonCodeList || [];
    },
    /** 获取菜单列表 */
    async getAuthMenuList(moduleId: number | string) {
      const { data } = await userCenterApi.getAuthMenuList({ id: moduleId });
      this.authMenuList = data;
    },
    /** 账号密码登录 */
    async loginPwd(model: Login.LoginForm) {
      this.loginLoading = true;
      // 登录接口
      await loginApi
        .login(model)
        .then(res => {
          if (res.data) {
            this.loginSuccess(res.data); //登录成功
          }
        })
        .catch(err => {
          return Promise.reject(err);
        })
        .finally(() => {
          this.loginLoading = false;
        });
    },
    /** 登录成功后的操作 */
    async handleActionAfterLogin() {
      await initDynamicRouter()
        .then(path => {
          // 初始化动态路由
          const tabsStore = useTabsStore();
          const { userInfo } = useUserStore();
          const keepAliveStore = useKeepAliveStore();
          // 3.清空 tabs、keepAlive 数据
          tabsStore.setTabs([]);
          keepAliveStore.setKeepAliveName([]);
          // 4.跳转到首页
          router.push(path);
          ElNotification({
            title: getTimeState(),
            message: "欢迎 " + userInfo?.name + ",现在时间是" + getCurrentDateTime(),
            type: "success",
            duration: 3000
          });
        })
        .catch(err => {
          console.log("[ err ] >", err);
          ElNotification({
            title: "系统错误",
            message: "系统错误,请联系系统管理员！",
            type: "warning",
            duration: 3000
          });
        });
    },
    /** 登录请求成功 */
    loginSuccess(data: Login.Login) {
      const { defaultModule, moduleList, orgList } = data;
      const userStore = useUserStore();
      if (orgList) {
        userStore.setOrgList(orgList);
      }
      this.SetModuleList(moduleList); // 设置模块列表
      if (moduleList.length === 1) {
        // 如果只有一个模块，直接登录
        userStore.setModule(moduleList[0].id); //存储选择的模块
        this.handleActionAfterLogin(); //登录成功后的操作
        return;
      } else if (defaultModule && moduleList.find(item => item.id === defaultModule)) {
        userStore.setModule(defaultModule); //存储选择的模块为默认模块
        this.handleActionAfterLogin(); //登录成功后的操作
      } else {
        this.showChooseModule = true; //开启选择模块
      }
    }
  }
});
