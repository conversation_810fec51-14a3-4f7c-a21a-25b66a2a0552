<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, OfficeBuilding, Calendar, Document } from '@element-plus/icons-vue';
import type { UnitPackage, PackageItem, SearchForm, Pagination, CompanyTimesUI } from './interface';
import { companyManagementApi } from '@/api/modules';
import { usePackages } from './models/usePackages';
import { usePackageQuery } from './models/usePackageQuery';
import { useCompanys } from './models/useCompanys';
import { useCompanyTimes } from './models/useCompanyTimes';
import { usePagination } from "./models/usePagination";

const { init: initPackages, packages, searchPackages } = usePackages();

const { packageQuery } = usePackageQuery();

const { companys, searchCompanys } = useCompanys();

const { companyTimes, getCompanyTimes, init: initCompanyTimes } = useCompanyTimes()

const { pagination } = usePagination()


// 表格数据
const loading = ref(false);

// 可用体检项目（用于详情展示）
const availableItems = ref<PackageItem[]>([]);

// 单位选择相关

const unitLoading = ref(false);

// 选中的套餐和详情数据
const selectedPackage = ref<UnitPackage | null>(null);
const activeTab = ref("basic");
const basicItems = ref<PackageItem[]>([]);
const additionalItems = ref<PackageItem[]>([]);
const packageItems = ref<any[]>([]);


onMounted(() => {

  initDate();

});


// 初始化数据
const initDate = async () => {

  //初始化默认单位信息
  await searchCompanys("");
  packageQuery.value.companyCode = companys.value[0].companyCode;

  //初始化默认单位信息

  companyTimes.value = await initCompanyTimes(packageQuery.value.companyCode);
  packageQuery.value.companyTimes = companyTimes.value[0].companyTimes

  const { companyCode } = packageQuery.value

  //初始化单位套餐信息
  await initPackages(companyCode, packageQuery.value.companyTimes);
  pagination.value.total = packages.value.length;
}




// 搜索套餐列表
const fetchPackageList = async (companyCode: string, companyTimes: number) => {
  packages.value = []
  packages.value = await searchPackages(companyCode, companyTimes);

  pagination.value.total = packages.value.length;
};




// 点击选择单位触发默认数据
const changeUnit = async (companyCode: string) => {
  packages.value = []

  const companyTimesData = await getCompanyTimes(companyCode);
  pagination.value.total = 0;
  const [firstTimes] = companyTimesData;
  packageQuery.value.companyTimes = firstTimes?.companyTimes ?? null;
  if (!!firstTimes?.companyTimes) {

    packages.value = await searchPackages(companyCode, companyTimesData[0]?.companyTimes);
    pagination.value.total = packages.value.length;
  }

};


// 点击行选择套餐
const handleRowClick = (row: any) => {
  selectedPackage.value = row;
  fetchPackageDetails(row.clusCode);
};

// 获取套餐详情
const fetchPackageDetails = async (clusCode: string) => {
  try {
    // 模拟API调用获取基础套餐详情
    const { data } = await companyManagementApi.getCompanyClusterComb(clusCode);
    basicItems.value = data

  } catch (error) {
    console.error("获取套餐详情失败:", error);
    ElMessage.error("获取套餐详情失败");
  }
};

// 这些函数已被删除，因为不再需要新增套餐功能



// 删除套餐功能已移除

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1;
  //fetchPackageList(packageSearchForm.value.companyCode, packageSearchForm.value.companyTimes);
};

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page;
  // fetchPackageList(packageSearchForm.value.companyCode, packageSearchForm.value.companyTimes);
};


</script>

<template>
  <div class="unit-package-container">
    <!-- 左右布局 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧：套餐列表 -->
      <el-col :span="6">
        <el-card class="package-list-card" shadow="never">
          <!-- 查询表单 -->
          <div class="search-form">

            <el-form :model="packageQuery" size="small" label-width="70px" class="search-form-content">
              <el-form-item label="单位名称" class="form-item">
                <el-select v-model="packageQuery.companyCode" placeholder="请选择或搜索单位" clearable filterable remote
                  @change="changeUnit(packageQuery.companyCode)" :loading="unitLoading" style="width: 100%"
                  class="custom-select" size="default">
                  <template #prefix>
                    <el-icon class="input-icon">
                      <OfficeBuilding />
                    </el-icon>
                  </template>
                  <el-option v-for="unit in companys" :key="unit.companyCode" :label="unit.companyName"
                    :value="unit.companyCode" />
                </el-select>
              </el-form-item>

              <el-form-item label="体检次数" class="form-item">
                <el-select v-model="packageQuery.companyTimes" placeholder="请选择体检次数" clearable style="width: 100%"
                  class="custom-select" @change="fetchPackageList(packageQuery.companyCode, packageQuery.companyTimes)"
                  size="default">
                  <template #prefix>
                    <el-icon class="input-icon">
                      <Calendar />
                    </el-icon>
                  </template>

                  <el-option v-for="item in companyTimes" :key="item.companyTimes" :label="`第${item.companyTimes}次体检`"
                    :value="item.companyTimes" />
                </el-select>
              </el-form-item>

              <el-form-item label="套餐名称" class="form-item">
                <el-input v-model="packageQuery.packageName" placeholder="请输入套餐名称关键词" clearable style="width: 100%"
                  class="custom-input" size="default">
                  <template #prefix>
                    <el-icon class="input-icon">
                      <Document />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>

              <!-- <el-form-item class="form-buttons">
                <el-button type="primary" @click="handlePackageSearch" size="small" class="search-btn">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
                <el-button @click="handlePackageReset" size="small" class="reset-btn">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item> -->
            </el-form>
          </div>

          <!-- 套餐表格 -->
          <el-table :data="packages" border stripe v-loading="loading" :header-cell-style="{
            background: '#f5f7fa',
            color: '#303133',
            fontWeight: 'bold'
          }" @row-click="handleRowClick" :highlight-current-row="true" height="60vh">
            <el-table-column prop="clusCode" label="套餐编码" width="120" />
            <el-table-column prop="clusName" label="套餐名称" show-overflow-tooltip />
            <!-- <el-table-column prop="companyTimes" label="体检次数" width="90">
              <template #default="scope">
                第{{ scope.row.companyTimes }}次
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="70">
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ scope.row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column> -->
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50]" layout="total, sizes, prev, pager, next" :total="pagination.total" background
              small @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：套餐详情 -->
      <el-col :span="18">
        <el-card class="package-detail-card" shadow="never" v-if="selectedPackage">
          <template #header>
            <div class="card-header">
              <span class="title">{{ selectedPackage.clusName }} - 套餐详情</span>
              <div class="header-actions">
                <!-- <el-button
                  :type="selectedPackage.status === 1 ? 'danger' : 'success'"
                  size="small"
                  @click="handleToggleStatus(selectedPackage)"
                >
                  {{ selectedPackage.status === 1 ? '禁用套餐' : '启用套餐' }}
                </el-button> -->
              </div>
            </div>
          </template>

          <!-- 套餐基本信息 -->
          <div class="package-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="套餐名称">{{ selectedPackage.clusName }}</el-descriptions-item>
              <el-descriptions-item label="体检次数">第{{ selectedPackage.companyTimes }}次体检</el-descriptions-item>


              <!-- <el-descriptions-item label="套餐价格">¥{{ selectedPackage.packagePrice }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="selectedPackage.status === 1 ? 'success' : 'danger'">
                  {{ selectedPackage.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间" :span="2">{{ selectedPackage.createTime }}</el-descriptions-item>
              <el-descriptions-item label="套餐描述" :span="2">
                {{ selectedPackage.description || '暂无描述' }}
              </el-descriptions-item> -->
            </el-descriptions>
          </div>

          <!-- 套餐项目详情 -->
          <div class="package-items-detail">
            <el-tabs v-model="activeTab" class="detail-tabs">
              <!-- 基础项目 -->
              <el-tab-pane label="基础项目" name="basic">
                <el-table :data="basicItems" border stripe max-height="600">
                  <el-table-column prop="combCode" label="项目编码" width="120" />
                  <el-table-column prop="combName" label="项目名称" />
                  <el-table-column prop="price" label="价格" width="100" />


                </el-table>
              </el-tab-pane>

              <!-- 加收项目 -->
              <el-tab-pane label="加收项目" name="additional">
                <el-table :data="additionalItems" border stripe min-height="600">
                  <el-table-column type="index" width="60" label="序号" />
                  <el-table-column prop="itemCode" label="项目编码" width="120" />
                  <el-table-column prop="itemName" label="项目名称" />
                  <el-table-column prop="department" label="科室" width="120" />
                  <el-table-column prop="itemPrice" label="加收价格" width="100">
                    <template #default="scope"> ¥{{ scope.row.itemPrice }} </template>
                  </el-table-column>
                  <el-table-column prop="description" label="说明" show-overflow-tooltip />
                </el-table>
              </el-tab-pane>

              <!-- 加项包 -->
              <el-tab-pane label="加项包" name="package">
                <el-table :data="packageItems" border stripe min-height="600">
                  <el-table-column type="index" width="60" label="序号" />
                  <el-table-column prop="packageCode" label="加项包编码" width="120" />
                  <el-table-column prop="packageName" label="加项包名称" />
                  <el-table-column prop="packagePrice" label="加项包价格" width="120">
                    <template #default="scope"> ¥{{ scope.row.packagePrice }} </template>
                  </el-table-column>
                  <el-table-column prop="itemCount" label="包含项目" width="100">
                    <template #default="scope"> {{ scope.row.itemCount }}项 </template>
                  </el-table-column>
                  <el-table-column prop="description" label="说明" show-overflow-tooltip />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>

        <!-- 未选择套餐时的提示 -->
        <el-card class="empty-card" shadow="never" v-else>
          <el-empty description="请选择左侧套餐查看详情" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.unit-package-container {
  .search-card {
    margin-bottom: 20px;
  }

  .main-content {
    height: 80vh;
  }

  .package-list-card {
    height: 100%;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }

    .search-form {
      margin-bottom: 15px;
      overflow: hidden;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgb(102 126 234 / 15%);

      .search-form-header {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: rgb(255 255 255 / 10%);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgb(255 255 255 / 10%);

        .search-icon {
          margin-right: 8px;
          font-size: 18px;
          color: #ffffff;
        }

        .search-title {
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          letter-spacing: 0.5px;
        }
      }

      .search-form-content {
        padding: 5px;
        background: rgb(255 255 255 / 95%);
        backdrop-filter: blur(10px);

        :deep(.el-form-item) {
          margin-bottom: 16px;

          &.form-buttons {
            margin-top: 8px;
            margin-bottom: 0;
            text-align: center;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }

        :deep(.el-form-item__label) {
          font-size: 13px;
          font-weight: 500;
          line-height: 32px;
          color: #606266;
        }

        :deep(.custom-select) {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
            }

            &.is-focus {
              box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
            }
          }

          .input-icon {
            font-size: 14px;
            color: #667eea;
          }
        }

        :deep(.custom-input) {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgb(102 126 234 / 20%);
            }

            &.is-focus {
              box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
            }
          }

          .input-icon {
            font-size: 14px;
            color: #667eea;
          }
        }

        .search-btn {
          padding: 8px 20px;
          margin-right: 12px;
          font-weight: 500;
          border: none;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 6px 16px rgb(102 126 234 / 40%);
            transform: translateY(-2px);
          }

          &:active {
            transform: translateY(0);
          }
        }

        .reset-btn {
          padding: 8px 20px;
          font-weight: 500;
          color: #606266;
          background: #ffffff;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            background: #f5f7fa;
            border-color: #c0c4cc;
            box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding: 10px 0;
      margin-top: 15px;
      border-top: 1px solid #e4e7ed;
    }

    :deep(.el-table) {
      .el-table__row {
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .current-row {
        background-color: #ecf5ff !important;

        &:hover {
          background-color: #ecf5ff !important;
        }
      }
    }
  }

  .package-detail-card {
    height: 100%;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .package-info {
      margin-bottom: 20px;
    }

    .package-items-detail {
      .detail-tabs {
        :deep(.el-tabs__content) {
          padding: 15px 0;
        }
      }
    }

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow-y: auto;
    }
  }

  .empty-card {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    :deep(.el-card__body) {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}

// 全局样式调整
:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        font-weight: bold;
        color: #303133;
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
