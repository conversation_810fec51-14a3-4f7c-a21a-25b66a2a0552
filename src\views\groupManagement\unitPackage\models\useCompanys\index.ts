import { companyManagementApi } from "@/api";
import { usePagination } from "../usePagination";
import { usePackageQuery } from "../usePackageQuery";

export const useCompanys = () => {
  const companys = ref([]);
  const { pagination, handleCurrentChange, handleSizeChange, reset: resetPagination } = usePagination();
  const { packageQuery, reset: resetPackageQuery } = usePackageQuery();

  async function searchCompanys(searchKey: string) {
    const { data } = await companyManagementApi.page({
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      searchKey
    });

    const { list, total } = data;
    companys.value = list;
    return { list, total };
  }

  return {
    companys,
    searchCompanys
  };
};
