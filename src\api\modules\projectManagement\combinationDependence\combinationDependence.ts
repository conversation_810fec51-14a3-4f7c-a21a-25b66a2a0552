/**
 * @description 组合依赖接口
 */
import { CombinationDependence } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/admin/basiccode/");

const combinationDependenceApi = {
  /** 组合依赖关系查询 */
  page() {
    return http.post<CombinationDependence.CombinationDependenceInfo[]>("getmapcombdependence", {});
  },
  /** 删除组合依赖关系 */
  delete(params: string[]) {
    return http.post<boolean>("deletecombdependence", params);
  },
  /** 获取组合依赖明细 */
  detail(params: {}) {
    return http.post<CombinationDependence.CombinationDependenceDetailInfo[]>("getcombdependencedetail", {}, { params });
  },
  /** 新增/修改依赖组合 */
  submitForm(params: {}) {
    return http.post("savecombdependencedetail", params);
  }
};

export { combinationDependenceApi };
