<!-- 表单-->
<template>
  <div>
    <form-container v-model="visible" :title="`${sysProps.opt}资讯`" form-size="500px" @close="onClose">
      <el-form
        ref="sysUserFormRef"
        :rules="rules"
        :disabled="sysProps.disabled"
        :model="sysProps.record"
        :hide-required-asterisk="sysProps.disabled"
        label-width="auto"
        label-suffix=" :"
      >
        <el-form-item prop="标题">
          <el-row :gutter="16">
            <el-col :span="24">
              <s-form-item label="标题" prop="account">
                <s-input v-model="sysProps.record.title"></s-input>
              </s-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="缩列图" prop="diagramUrl">
          <UploadImg
            v-model:image-url="formData_diagramUrl.imageUrl"
            width="135px"
            height="135px"
            :file-size="2"
            :auto-upload="false"
            @file-change="handleFileChange_diagramUrl"
          >
            <template #empty>
              <el-icon>
                <Avatar />
              </el-icon>
              <span>请上传图片jpg/png</span>
            </template>
          </UploadImg>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch v-model="states" />
        </el-form-item>

        <el-form-item label="类型" prop="typeCode">
          <el-select v-model="sysProps.record.typeCode" placeholder="请选择资讯类型" clearable>
            <el-option v-for="item in peClsOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script setup lang="ts">
import { sysRealTimeInfo, realTimeInfoApi } from "@/api";
import { SysDictEnum, FormOptEnum } from "@/enums";
import { FormInstance, UploadFile } from "element-plus";
import { required } from "@/utils/formRules";
import { useDictStore } from "@/stores/modules";

//上传地址
const url = import.meta.env.VITE_API_URL as string;
/**存放图片，用于统一上传 */
const formData_diagramUrl = ref({
  imageUrl: "", // 自动接收子组件传递的图片地址
  rawFile: null as File | null
});
/**存放图片 */
const handleFileChange_diagramUrl = (file: File) => {
  formData_diagramUrl.value.rawFile = file;
};

/**重置图片为初始值 */
const resetUpload = () => {
  formData_diagramUrl.value.rawFile = null;
  formData_diagramUrl.value.imageUrl = "";
};

/**是否显示表单 */
const visible = ref(false);
/**表单参数 */
const sysProps = reactive<FormProps.Base<sysRealTimeInfo.realTimeInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});
/**状态 */
const states = ref(true);
const selectValue = ref("");
const dictStore = useDictStore(); //字典仓库
/**下拉框 */
const peClsOptions = dictStore.getDictList(SysDictEnum.REAL_TIME_INFO);
/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<sysRealTimeInfo.realTimeInfo>) {
  Object.assign(sysProps, props); //合并参数
  if (props.opt == FormOptEnum.EDIT) {
    if (props.record.status == "Y") {
      states.value = true;
    } else {
      states.value = false;
    }
    formData_diagramUrl.value.imageUrl = props.record.diagramUrl ? url + props.record.diagramUrl : "";
    console.log("sysProps", sysProps.record);
  } else {
    {
      //新增，设置默认值
    }
  }
  visible.value = true; //显示表单
}

/**表单验证规则 */
const rules = reactive({
  title: [required("请输入标题")],
  typeCode: [required("请选择类型")],
  status: [required("请选择是否启用")],
  diagramUrl: [required("请选择图片jpg/png")]
});
// 提交数据（编辑）
const sysUserFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  //上传图片
  if (formData_diagramUrl.value.rawFile) {
    const formData = new FormData();
    formData.append("file", formData_diagramUrl.value.rawFile);
    sysProps.record.diagramUrl = await UploadRealTimeInfoImg(formData);
  } else if (!formData_diagramUrl.value.imageUrl) {
    //删除图片
    sysProps.record.diagramUrl = "";
  }
  // console.log("sysProps.record", sysProps.record);
  if (states.value) {
    sysProps.record.status = "Y";
  } else {
    sysProps.record.status = "N";
  }
  //提交表单
  await realTimeInfoApi
    .EditRealTimeInfo(
      {
        Title: sysProps.record.title,
        TypeCode: sysProps.record.typeCode,
        Content: "",
        DiagramUrl: sysProps.record.diagramUrl,
        Status: sysProps.record.status,
        Id: sysProps.record.id
      },
      sysProps.record.id != undefined
    )
    .then(() => {
      ElMessage.success("成功!");
      sysProps.successful!(); //调用父组件的successful方法
      resetUpload();
    })
    .finally(() => {
      onClose();
    });
}

/** 上传图片 */
async function UploadRealTimeInfoImg(formData: FormData): Promise<string> {
  try {
    const res = await realTimeInfoApi.UploadRealTimeInfoImg(formData);
    if (res.code == "200") {
      // console.log("data", res.data);
      return res.data.virtualPath;
    } else {
      ElMessage.error("上传失败!");
      throw new Error("上传失败!");
    }
  } catch (error) {
    // console.error("上传错误:", error.message)
    ElMessage.error("上传请求失败:" + error.message);
    throw error; // 继续抛出异常
  }
}

/** 关闭表单*/
function onClose() {
  resetUpload();
  visible.value = false;
}

/**暴露给父组件的方法 */
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.upload-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.upload-empty {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  line-height: 30px;
  color: var(--el-color-info);
  .el-icon {
    font-size: 28px;
    color: var(--el-text-color-secondary);
  }
}
</style>
