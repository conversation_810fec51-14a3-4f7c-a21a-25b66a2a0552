/**
 * @description 用户个人中心
 */
import { moduleRequest } from "@/api/request";
import { ReqId, UserCenter, Login, Menu } from "@/api";
const http = moduleRequest("/sys/userCenter/");

const userCenterApi = {
  /** 获取用户菜单 */
  getAuthMenuList(params: ReqId) {
    return http.get<Menu.MenuInfo[]>("loginMenu", params, { loading: false });
  },
  /** 设置默认模块 */
  setDefaultModule(params: UserCenter.ResModuleDefault) {
    http.post("setDefaultModule", params, { loading: false });
  },
  /** 修改用户密码 */
  updatePassword(params: UserCenter.ReqUpdatePassword) {
    return http.post("updatePassword", params);
  },
  /** 更新头像 */
  updateAvatar(params: any) {
    return http.post<string>("updateAvatar", params, { loading: false });
  },
  /** 更新用户信息 */
  updateUserInfo(params: Login.LoginUserInfo) {
    return http.post<string>("updateUserInfo", params, { loading: false });
  }
};

/** 管理按键权限码 */
const userButtonCode = {
  // 用户部分
  操作用户: "cZyh",
  授权角色: "sQjs",
  重置密码: "cZmm",

  //角色功能
  操作角色: "cZjs",
  授权菜单: "sQcd",

  //模块功能
  操作模块: "cZmk",

  //菜单功能
  操作菜单: "cZcd",
  操作按键: "cZaj",

  //日志功能
  操作日志: "cZrz",

  //字典功能
  操作字典: "cZzd",

  //系统配置
  操作配置: "cZpz",

  //组织机构配置
  操作机构: "cZjg"
};

export { userCenterApi, userButtonCode };
