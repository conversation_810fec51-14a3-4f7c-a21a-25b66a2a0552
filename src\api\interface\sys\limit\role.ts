/**
 * @description 角色管理接口
 */
import { ReqPage } from "@/api/interface";
import { ReqId, SysUser } from "@/api/interface";

export namespace SysRole {
  /** 角色分页查询 */
  export interface Page extends ReqPage {}

  /** 角色信息 */
  export interface SysRoleInfo {
    /** 角色id */
    id: number | string;
    /** 角色名称 */
    name: string;
    /** 角色编码 */
    code: string;
    /** 状态 */
    status: string;
    /** 排序码 */
    sortCode: number;
    /** 关联的院区*/
    extJson: string;
    /** 用户信息 */
    userList: SysUser.SysUserInfo[];
  }

  /** 角色树 */
  export interface SysRoleTree {
    /** id */
    id: number | string;
    /** 名称 */
    name: string;
    /** 是否是角色 */
    isRole: boolean;
    /** 子集 */
    children: SysRoleTree[];
  }

  /** 角色授权资源树 */
  export interface ResTreeSelector {
    /** 模块id */
    id: number | string;
    /** 模块名称 */
    title: string;
    /** 图标 */
    icon: string;
    /** 模块下菜单集合 */
    menu: RoleGrantResourceMenu[];
  }

  /** 角色授权资源菜单信息 */
  export interface RoleGrantResourceMenu {
    /** 菜单id */
    id: number | string;
    /** 父id */
    parentId: number | string;
    /** 父名称 */
    parentName: string;
    /** 模块名称 */
    title: string;
    /** 模块id */
    module: number;
    /** 菜单下按钮集合 */
    button: RoleGrantResourceButton[];
    /** 父级是否选中 */
    parentCheck: boolean;
    /** 是否选中 */
    nameCheck: boolean;
  }

  /** 角色授权资源按钮信息 */
  export interface RoleGrantResourceButton {
    /** 按钮id */
    id: number | string;
    /** 名称 */
    title: string;
    /** 是否被选中 */
    check: boolean;
  }

  /** 角色拥有资源 */
  export interface RoleOwnResource {
    /** id */
    id: number | string;
    /** 已授权资源信息 */
    grantInfoList: RelationRoleResource[];
  }

  /** 角色有哪些资源 */
  export interface RelationRoleResource {
    /** 菜单id */
    menuId: number | string;
    /** 按钮信息 */
    buttonInfo: number[] | string[];
  }

  /** 角色权限关系扩展 */
  export interface RelationRolePermission {
    /** 数据范围 */
    scopeCategory: string;
    /** 自定义机构范围列表 */
    scopeDefineOrgIdList: string[] | number[];
    /** 接口Url */
    apiUrl: string;
  }

  /** 角色拥有权限 */
  export interface RoleOwnPermission {
    /** id */
    id: number | string;
    /** 已授权权限信息 */
    grantInfoList: RelationRolePermission[];
  }

  /** 角色授权资源请求参数 */
  export interface GrantResourceReq extends ReqId {
    /** 授权资源信息 */
    grantInfoList: RelationRoleResource[];
  }

  /** 角色授权资源请求参数 */
  export interface GrantPermissionReq extends ReqId {
    /** 授权资源信息 */
    grantInfoList: RelationRolePermission[];
  }

  /** 角色授权权限信息 */
  export interface RoleGrantPermission {
    /** api地址 */
    api: string;
    /** 是否被选中 */
    check: boolean;
  }

  /** 角色授权用户请求参数 */
  export interface GrantUserReq extends ReqId {
    /** 授权资源信息 */
    grantInfoList: number[] | string[];
  }

  /** 角色选择器请求参数  */
  export interface RoleSelectorReq {
    /** 角色名称 */
    account?: string;
    /** 组织id */
    orgId: string | number;
  }
}
