<template>
  <div class="main-box">
    <div class="table-box page-container">
      <div class="card package-container">
        <SearchForm ref="searchFormRef" @search="getPage" @open="onOpen(FormOptEnum.ADD)" />
        <el-table
          :data="pageTableData"
          style="width: 100%"
          height="100%"
          border
          highlight-current-row
          ref="tableRef"
          @current-change="handleCurrentRowChange"
        >
          <el-table-column prop="clusCode" label="套餐编码" align="center" width="120" />
          <el-table-column prop="clusName" label="套餐名称" align="center" />
          <el-table-column fixed="right" label="操作" width="80" align="center">
            <template #default="scope">
              <!-- <el-button link :icon="EditPen" type="primary" @click="onOpen(FormOptEnum.EDIT, scope.row)">编辑</el-button> -->
              <el-button link type="danger" :icon="Delete" @click="onDelete([scope.row.id], `删除【${scope.row.clusName}】`)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script lang="ts" setup name="individualPackage">
import { individualPackageApi, IndividualPackage } from "@/api";
import { FormOptEnum } from "@/enums";
import { useHandleData } from "@/hooks/useHandleData";
import Form from "./components/form/index.vue";
import SearchForm from "./components/form/searchForm.vue";
import { Delete } from "@element-plus/icons-vue";
import type { TableInstance } from "element-plus";

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);
/** 表格引用 */
const tableRef = ref<TableInstance | null>(null);
/** 搜索表单引用 */
const searchFormRef = ref<InstanceType<typeof SearchForm> | null>(null);
/** 表格数据 */
const pageTableData = ref<IndividualPackage.IndividualPackageInfo[]>([]);
/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | IndividualPackage.IndividualPackageInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
  tableRef.value?.setCurrentRow(null);
}
/**
 * 当前行改变
 * @param record  记录
 */
function handleCurrentRowChange(record: IndividualPackage.IndividualPackageInfo) {
  if (record?.clusCode) {
    formRef.value?.onOpen({ opt: FormOptEnum.EDIT, record: record, successful: RefreshTable });
  }
}
/**
 * 刷新表格
 */
function RefreshTable() {
  getPage();
}
/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(individualPackageApi.delete, { ids }, msg);
  RefreshTable();
  if (formRef.value?.visible) {
    formRef.value?.onClose();
  }
}
/**
 * 获取分页数据
 */
async function getPage() {
  const { data } = await individualPackageApi.page({
    searchKey: searchFormRef.value?.searchForm.searchKey,
    peCls: searchFormRef.value?.searchForm.peCls
  });
  if (data) {
    pageTableData.value = data;
  }
}
onMounted(() => {
  getPage();
});
</script>

<style lang="scss" scoped>
.main-box {
  .page-container {
    display: grid;
    grid-template-columns: 450px 1fr;
    gap: 10px;
    .package-container {
      display: grid;
      grid-template-rows: auto 1fr;
      grid-template-columns: 1fr;
    }
  }
}
</style>
