/**
 * @description 号源管理接口
 */

import { ReqPage } from "@/api/interface";

export namespace numberSource {
  /**号源分页查询 */
  export interface Page extends ReqPage {
    /** 号源名称 */
    name?: string;
    /** 号源日期 */
    sourceDate?: string;
    /** 号源状态 */
    status?: string;
  }

  /** 号源信息 */
  export interface SourceInfo {
    /** id */
    id: number | string;
    /** 号源名称 */
    name: string;
    /** 号源日期 */
    sourceDate: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
    /** 号源容量 */
    capacity: number;
    /** 已预约数量 */
    reserved: number;
    /** 号源状态 */
    status: string;
    /** 备注 */
    remark?: string;
    /** 排序 */
    sortCode: number;
    /** 创建时间 */
    createTime: string;
  }

  /** 号源列表 */
  export interface SourceList {
    /** 总容量   */
    totalCapacity: number;
    /** 已用数量 */
    usedCapacity: number;
    /** 可用数量 */
    availableCapacity: number;
    /** 日期 */
    date: string;
    /** 星期（1-7） */
    week: number;
    /** 是否休假 */
    isVacation: string;
    /** 关联的时间段id */
    timeSlotID: string;
  }

  /** 编辑号源入参类型 */
  export interface SourceInput {
    /** 号源类型编码 */
    sourceTypeID: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
    /** 时段编码 */
    timeSlotID: string;
    /** 是否休假 */
    statu: string;
    /** 号源总量 */
    totalCapacity: number;
    /** 单位编码 */
    usedCapacity: number;
    /** 重点项目编码 */
    codeItemCode: string;
  }
}
