.home {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 20px;

  // 数据面板样式
  .data-panel {
    margin-bottom: 20px;
    .data-card {
      height: 120px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        box-shadow: 0 5px 15px rgb(0 0 0 / 10%);
        transform: translateY(-5px);
      }
      .data-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .data-title {
          font-size: 16px;
          font-weight: bold;
          color: #606266;
        }
        .data-icon {
          font-size: 24px;
          color: #409eff;
        }
      }
      .data-content {
        .data-value {
          margin-bottom: 5px;
          font-size: 28px;
          font-weight: bold;
          color: #303133;
        }
        .data-compare {
          display: flex;
          align-items: center;
          font-size: 13px;
          color: #909399;
          .up {
            margin-left: 5px;
            color: #67c23a;
          }
          .down {
            margin-left: 5px;
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 图表区域样式
  .chart-panel {
    margin-bottom: 20px;
    .chart-card {
      margin-bottom: 20px;
      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }
      }
      .chart-container {
        width: 100%;
        height: 350px;
      }
    }
  }

  // 欢迎图片区域
  .welcome-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    .home-bg {
      width: 70%;
      max-width: 1000px;
      margin-bottom: 20px;
    }
  }
}

// 响应式调整
@media screen and (width <= 768px) {
  .home {
    .data-panel {
      .data-card {
        height: auto;
        margin-bottom: 15px;
      }
    }
    .chart-panel {
      .chart-card {
        .chart-container {
          height: 250px;
        }
      }
    }
    .welcome-section {
      .home-bg {
        width: 90%;
      }
    }
  }
}
