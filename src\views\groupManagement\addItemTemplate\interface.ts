// 加项模板相关接口定义

// 加项模板信息
export interface AddItemTemplate {
  id: string;
  templateCode: string;
  templateName: string;
  description?: string;
  itemCount: number;
  totalPrice: number;
  status: number; // 0: 禁用, 1: 启用
  createTime: string;
  updateTime?: string;
  items?: TemplateItem[];
}

// 模板项目信息
export interface TemplateItem {
  id: string;
  itemCode: string;
  itemName: string;
  itemPrice: number;
  department: string;
  description?: string;
}

// 搜索表单
export interface SearchForm {
  templateName: string;
  templateCode: string;
}

// 分页信息
export interface Pagination {
  currentPage: number;
  pageSize: number;
  total: number;
}

// 模板表单
export interface TemplateForm {
  id: string;
  templateName: string;
  description: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应类型
export interface PageResponse<T = any> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

// 步骤状态
export interface StepStatus {
  current: number;
  total: number;
  completed: boolean[];
}
