<template>
  <div class="search-form">
    <el-form :model="searchForm" class="" ref="searchFormRef" label-suffix=" :">
      <el-form-item label="套餐名称" prop="searchKey">
        <el-input v-model="searchForm.searchKey" placeholder="请输入套餐名称" clearable />
      </el-form-item>
      <el-form-item label="体检分类" prop="peCls">
        <el-select v-model="searchForm.peCls" placeholder="请选择体检分类" clearable>
          <el-option v-for="item in peClsOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="operation">
          <el-button :icon="Search" type="primary" @click="onSearch">搜索</el-button>
          <el-button :icon="CirclePlus" type="primary" @click="emit('open')">新增套餐</el-button>
          <el-button type="success" icon="Refresh" @click="onSyncCluster">同步套餐</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { SysDictEnum } from "@/enums";
import { useDictStore } from "@/stores/modules";
import { Search, CirclePlus } from "@element-plus/icons-vue";
import { FormInstance } from "element-plus";
import { individualPackageApi } from "@/api";

/** 搜索表单 */
const searchForm = reactive({
  searchKey: "",
  peCls: ""
});
const dictStore = useDictStore(); //字典仓库
// 状态选项
const peClsOptions = dictStore.getDictList(SysDictEnum.PE_CLS);
/** 搜索表单ref */
const searchFormRef = ref<FormInstance>();

/** 定义组件对外暴露的方法 */
const emit = defineEmits(["search", "open"]);
/** 搜索 */
function onSearch() {
  emit("search");
}
/** 同步 */
async function onSyncCluster() {
  await individualPackageApi.SyncClusterAndCombs().then(() => {
    ElMessage.success("同步成功");
    onSearch();
  });
}
/** 打开 */
function onOpen() {
  emit("open");
}
defineExpose({
  searchForm,
  onSearch
});
</script>

<style lang="scss" scoped>
.search-form {
  .operation {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
