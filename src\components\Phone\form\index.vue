<template>
  <div class="checkup-container">
    <!-- 背景图片 -->
    <div class="background-image">
      <!-- 使用CSS渐变模拟建筑背景 -->
       <img src="./images/首页封面.png" />
    </div>

    <!-- 公司信息卡片 -->
    <div class="company-card">
      <div class="company-info">
        <div class="company-logo">
          <div class="logo-circle">
              <img v-if="unitInfo.imgData" :src="binaryToImageViaBase64(unitInfo.imgData)"  width="100%" height="100%" />
          </div>
        </div>
        <div class="company-details">
          <div class="company-name">{{unitInfo.companyName}}</div>
          <div class="validity-period">
            体检日期：<span class="period-text">{{unitInfo.validityPeriod}}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 体检须知卡片 -->
    <div class="notice-card">
      <div class="notice-header">
        <div class="notice-title">体检须知</div>
      </div>

      <div class="notice-content" >
          

        <div class="greeting" v-html="unitInfo.notes"></div>

        <!-- <div class="contact-info">
          <div class="contact-item">
            <span class="contact-label">电话：</span>
            <span class="contact-value">020-86268580</span>
          </div>

          <div class="contact-item">
            <span class="contact-label">地址：</span>
            <span class="contact-value">广州市黄埔区中山大学附属第三医院岭南医院。</span>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

interface UnitInfo {
  title: string;
  companyName: string;
  validityPeriod: string;
  notes: string;
  imgData: string;
  fileSize: number;
}
const unitInfo:UnitInfo =  inject('unitInfoFromUnitSetting')

function binaryToImageViaBase64(binaryData, mimeType = 'image/png') {
  const dataUrl = `data:${mimeType};base64,${binaryData}`;
   return dataUrl;
}
</script>

<style scoped lang="scss">
.checkup-container {
  position: relative;
  height: 600px;
  overflow-y: auto;


  // 背景图片
  .background-image {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 200px;
    overflow: hidden;
    img{
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

  }

  // 公司信息卡片
  .company-card {
    position: relative;
    z-index: 2;
    padding: 16px;
    margin: 160px 16px 16px;
    background: rgb(*********** / 95%);
    backdrop-filter: blur(10px);
    border-radius: 6px;
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
    .company-info {
      display: flex;
      align-items: center;
      .company-logo {
        margin-right: 12px;
        .logo-circle {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          .logo-text {
            font-size: 18px;
            font-weight: bold;
            color: white;
          }
        }
      }
      .company-details {
        flex: 1;
        .company-name {
          margin-bottom: 4px;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.3;
          color: #333333;
        }
        .validity-period {
          font-size: 12px;
          color: #666666;
          .period-text {
            font-weight: 500;
            color: #2196F3;
          }
        }
      }
    }
  }

  // 体检须知卡片
  .notice-card {
    position: relative;
    z-index: 2;
    margin: 0 16px 20px;
    overflow: hidden;
       background: url("./images/体检须知背景.png");
      background-size: cover;
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
    .notice-header {
      padding: 12px 16px;
      .notice-title {
        font-size: 18px;
        font-weight: 600;
        color: white;
        text-align: left;
      }
    }
    .notice-content {
      padding: 16px;
      margin: .2rem;
      background: #ffffff;
      .greeting {
        min-height: 200px;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        word-break: break-word;
        overflow-wrap: break-word;
        white-space: normal;
      }
      .appointment-info {
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 1.5;
        color: #555555;
        .date-highlight {
          font-weight: 600;
          color: #2196F3;
        }
      }
      .notice-list {
        margin-bottom: 20px;
        .notice-item {
          display: flex;
          margin-bottom: 12px;
          line-height: 1.6;
          .item-number {
            min-width: 20px;
            margin-right: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #555555;
          }
          .item-text {
            flex: 1;
            font-size: 14px;
            color: #555555;
          }
        }
      }
      .contact-info {
        padding-top: 16px;
        border-top: 1px solid #eeeeee;
        .contact-item {
          display: flex;
          margin-bottom: 8px;
          font-size: 14px;
          .contact-label {
            min-width: 40px;
            font-weight: 500;
            color: #666666;
          }
          .contact-value {
            flex: 1;
            line-height: 1.5;
            color: #333333;
          }
        }
      }
    }
  }
}

// 滚动条样式
.checkup-container::-webkit-scrollbar {
  width: 4px;
}
.checkup-container::-webkit-scrollbar-track {
  background: rgb(*********** / 10%);
}
.checkup-container::-webkit-scrollbar-thumb {
  background: rgb(*********** / 30%);
  border-radius: 2px;
}
</style>
