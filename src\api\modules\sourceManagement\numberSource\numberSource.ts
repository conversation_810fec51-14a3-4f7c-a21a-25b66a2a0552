/**
 * @description 号源管理
 */

import { moduleRequest } from "@/api/request";
import { ReqId, ResPage, ResultData, numberSource } from "@/api/interface";
const http = moduleRequest("/admin/numbersource/");

const numberSourceApi = {
  /** 获取号源分页 */
  page(params: numberSource.Page) {
    return http.get<ResPage<numberSource.SourceInfo>>("page", params);
  },
  /** 获取号源详情 */
  detail(params: ReqId) {
    return http.get<numberSource.SourceInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params: {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除号源 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 号源查询 */
  getNumberSource(params) {
    return http.post<ResultData<numberSource.SourceList[]>>("getnumbersource", params);
  },
  /** 号源编辑 */
  editNumberSource(params) {
    return http.post<ResultData>("editnumbersource", params);
  },
  /** 获取某一天号源 */
  getNumberSourceByDate(params) {
    return http.post<numberSource.SourceList[]>("getnumbersourcebydate", params);
  }
};

export { numberSourceApi };
