<!--
 * @Author: <PERSON>
 * @Date: 2025-04-27 11:14:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-30 17:53:54
 * @Description: 请填写简介
-->
<template>
  <div class="home">
    <!-- 数据面板 -->
    <div class="data-panel">
      <el-row :gutter="20">
        <!-- 体检人数 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card shadow="hover" class="data-card">
            <div class="data-header">
              <div class="data-title">体检人数</div>
              <el-icon class="data-icon"><User /></el-icon>
            </div>
            <div class="data-content">
              <div class="data-value">{{ checkupCount }}</div>
              <div class="data-compare">
                <span>较昨日</span>
                <span :class="checkupIncrease > 0 ? 'up' : 'down'">
                  {{ checkupIncrease > 0 ? "+" : "" }}{{ checkupIncrease }}%
                  <el-icon v-if="checkupIncrease > 0"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                </span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 体检项目数 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card shadow="hover" class="data-card">
            <div class="data-header">
              <div class="data-title">体检项目</div>
              <el-icon class="data-icon"><List /></el-icon>
            </div>
            <div class="data-content">
              <div class="data-value">{{ itemCount }}</div>
              <div class="data-compare">
                <span>较上月</span>
                <span :class="itemIncrease > 0 ? 'up' : 'down'">
                  {{ itemIncrease > 0 ? "+" : "" }}{{ itemIncrease }}%
                  <el-icon v-if="itemIncrease > 0"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                </span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 访问量 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card shadow="hover" class="data-card">
            <div class="data-header">
              <div class="data-title">访问量</div>
              <el-icon class="data-icon"><View /></el-icon>
            </div>
            <div class="data-content">
              <div class="data-value">{{ visitCount }}</div>
              <div class="data-compare">
                <span>较昨日</span>
                <span :class="visitIncrease > 0 ? 'up' : 'down'">
                  {{ visitIncrease > 0 ? "+" : "" }}{{ visitIncrease }}%
                  <el-icon v-if="visitIncrease > 0"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                </span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 预约数 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <el-card shadow="hover" class="data-card">
            <div class="data-header">
              <div class="data-title">预约数</div>
              <el-icon class="data-icon"><Calendar /></el-icon>
            </div>
            <div class="data-content">
              <div class="data-value">{{ appointmentCount }}</div>
              <div class="data-compare">
                <span>较昨日</span>
                <span :class="appointmentIncrease > 0 ? 'up' : 'down'">
                  {{ appointmentIncrease > 0 ? "+" : "" }}{{ appointmentIncrease }}%
                  <el-icon v-if="appointmentIncrease > 0"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="chart-panel">
      <el-row :gutter="20">
        <!-- 体检人数趋势图 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>体检人数趋势</span>
                <el-radio-group v-model="checkupTimeRange" size="small">
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="year">本年</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container" ref="checkupChartRef"></div>
          </el-card>
        </el-col>

        <!-- 体检项目分布图 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>体检项目分布</span>
              </div>
            </template>
            <div class="chart-container" ref="itemChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { ref, onMounted, onUnmounted, watch } from "vue";
import { User, List, View, Calendar, ArrowUp, ArrowDown } from "@element-plus/icons-vue";
import * as echarts from "echarts";

// 数据统计
const checkupCount = ref(1258);
const checkupIncrease = ref(5.2);
const itemCount = ref(156);
const itemIncrease = ref(-2.3);
const visitCount = ref(8562);
const visitIncrease = ref(12.5);
const appointmentCount = ref(325);
const appointmentIncrease = ref(8.7);

// 图表相关
const checkupTimeRange = ref("week");
const checkupChartRef = ref<HTMLElement | null>(null);
const itemChartRef = ref<HTMLElement | null>(null);
let checkupChart: echarts.ECharts | null = null;
let itemChart: echarts.ECharts | null = null;

// 初始化体检人数趋势图
const initCheckupChart = () => {
  if (!checkupChartRef.value) return;

  checkupChart = echarts.init(checkupChartRef.value);

  const weekData = [30, 42, 51, 34, 48, 62, 45];
  const monthData = [
    120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149, 210, 122, 133, 334, 198, 123, 125, 220, 201, 302, 198, 233
  ];
  const yearData = [900, 1200, 1500, 1800, 2100, 2400, 2000, 1800, 2200, 2500, 2300, 2100];

  const weekXAxis = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
  const monthXAxis = Array.from({ length: 30 }, (_, i) => `${i + 1}日`);
  const yearXAxis = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];

  let data = weekData;
  let xAxis = weekXAxis;

  if (checkupTimeRange.value === "month") {
    data = monthData;
    xAxis = monthXAxis;
  } else if (checkupTimeRange.value === "year") {
    data = yearData;
    xAxis = yearXAxis;
  }

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxis,
      axisLine: {
        lineStyle: {
          color: "#999"
        }
      },
      axisLabel: {
        color: "#666"
      }
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false
      },
      axisLabel: {
        color: "#666"
      },
      splitLine: {
        lineStyle: {
          color: "#eee"
        }
      }
    },
    series: [
      {
        name: "体检人数",
        type: "bar",
        barWidth: "60%",
        data: data,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#83bff6" },
            { offset: 0.5, color: "#188df0" },
            { offset: 1, color: "#188df0" }
          ])
        }
      }
    ]
  };

  checkupChart.setOption(option);
};

// 初始化体检项目分布图
const initItemChart = () => {
  if (!itemChartRef.value) return;

  itemChart = echarts.init(itemChartRef.value);

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      right: 10,
      top: "center",
      data: ["常规体检", "入职体检", "婚前体检", "孕前体检", "老年体检", "其他"]
    },
    series: [
      {
        name: "体检项目",
        type: "pie",
        radius: ["50%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "18",
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: "常规体检" },
          { value: 20, name: "入职体检" },
          { value: 15, name: "婚前体检" },
          { value: 10, name: "孕前体检" },
          { value: 12, name: "老年体检" },
          { value: 8, name: "其他" }
        ]
      }
    ]
  };

  itemChart.setOption(option);
};

// 监听时间范围变化
const handleTimeRangeChange = () => {
  initCheckupChart();
};

// 监听窗口大小变化，重新调整图表大小
const handleResize = () => {
  checkupChart?.resize();
  itemChart?.resize();
};

// 组件挂载时初始化图表
onMounted(() => {
  initCheckupChart();
  initItemChart();

  // 监听时间范围变化
  watch(checkupTimeRange, handleTimeRangeChange);

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

// 组件卸载时销毁图表实例
onUnmounted(() => {
  checkupChart?.dispose();
  itemChart?.dispose();

  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
