<!-- 表单-->
<template>
  <form-container v-model="visible" :title="`${sysOrgProps.opt}机构`" form-size="800px" @close="onClose">
    <el-form
      ref="sysOrgFormRef"
      :rules="rules"
      :disabled="sysOrgProps.disabled"
      :model="sysOrgProps.record"
      :hide-required-asterisk="sysOrgProps.disabled"
      label-width="auto"
      label-suffix=" :"
    >
      <s-form-item label=" LOGO " prop="avatar">
        <div class="avatar">
          <img :src="sysOrgProps?.record?.avatar" />
          <a style="cursor: pointer" @click="uploadAvatar">
            <div :class="sysOrgProps?.record?.avatar ? 'mask' : 'mask-notImg'">
              <el-icon>
                <Upload />
              </el-icon>
            </div>
          </a>
        </div>
        <crop-upload ref="cropUploadRef" :img-src="sysOrgProps?.record?.avatar" @successful="uploadSuccess"></crop-upload>
      </s-form-item>
      <s-form-item label="医院名称" prop="orgName">
        <s-input v-model="sysOrgProps.record.orgName"></s-input>
      </s-form-item>
      <s-form-item label="医院编码" prop="orgCode">
        <s-input v-model="sysOrgProps.record.orgCode" :disabled="sysOrgProps.record.id != undefined" placeholder="请填写医院编码"></s-input>
      </s-form-item>
      <s-form-item label="院区名称" prop="areaName">
        <s-input v-model="sysOrgProps.record.areaName"></s-input>
      </s-form-item>
      <s-form-item label="院区编码" prop="areaCode">
        <s-input v-model="sysOrgProps.record.areaCode" placeholder="请填写院区编码"></s-input>
      </s-form-item>
      <s-form-item label="信息描述" prop="describe">
        <s-input v-model="sysOrgProps.record.describe" placeholder="信息描述"></s-input>
      </s-form-item>
      <s-form-item label="医院地址" prop="address">
        <s-input v-model="sysOrgProps.record.address" placeholder="位置地址"></s-input>
      </s-form-item>
      <s-form-item label="联系电话" prop="telTag">
        <s-input v-model="sysOrgProps.record.telTag" placeholder="请填联系电话，逗号隔开"></s-input>
      </s-form-item>
      <s-form-item label="状态" prop="status">
        <s-radio-group v-model="sysOrgProps.record.status" :options="statusOptions" button />
      </s-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose"> 取消 </el-button>
      <el-button v-auth="userButtonCode.操作机构" v-show="!sysOrgProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
    </template>
  </form-container>
</template>

<script setup lang="ts">
import { SysOrg, sysOrgApi, userButtonCode } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance, ElMessage } from "element-plus";
import { CropUploadInstance } from "@/components/CropUpload/interface";
import { useDictStore } from "@/stores/modules";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS); // 通用状态选项

const cropUploadRef = ref<CropUploadInstance>();

/** 上传头像 */
function uploadAvatar() {
  if (!sysOrgProps?.record?.id) {
    alert("sss");
    return;
  }
  cropUploadRef.value?.show();
}

// 表单参数
const sysOrgProps = reactive<FormProps.Base<SysOrg.SysOrgInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  orgName: [required("请输入机构名称")],
  orgCode: [required("请输入机构编码")],
  areaName: [required("请输入院区名称")],
  areaCode: [required("请输入院区编码")],
  status: [required("请选择状态")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<SysOrg.SysOrgInfo>) {
  Object.assign(sysOrgProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    sysOrgProps.record.status = statusOptions[0].value;
  }
  visible.value = true; //显示表单
}

// 提交数据（新增/编辑）
const sysOrgFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  sysOrgFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    await sysOrgApi
      .submitForm(sysOrgProps.record, sysOrgProps.record.id != undefined)
      .then(() => {
        sysOrgProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

/** 上传成功回调 */
function uploadSuccess(data: { fileName: string; blobData: any }) {
  let result = null;
  // 如果没有文件名，就转换为file类型，否则就是file类型
  if (data.fileName === "") {
    result = blobToFile(data.blobData, "avatar.jpg");
  } else {
    // 转换为file类型
    result = new File([data.blobData], data.fileName, {
      type: "image/jpeg",
      lastModified: Date.now()
    });
  }
  let formData = new FormData();
  formData.append("id", sysOrgProps?.record?.id as string);
  formData.append("file", result);
  sysOrgApi.updateAvatar(formData).then(res => {
    if (res.code == "200") {
      sysOrgProps.record.avatar = res.data;
      ElMessage.success("更新成功");
    } else {
      ElMessage.warning(res.msg);
    }
  });
}

/** blob转file */
function blobToFile(blob: BlobPart, fileName: string) {
  return new File([blob], fileName, {
    type: "image/jpeg",
    lastModified: Date.now()
  });
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.avatar {
  position: relative;
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  a {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 50%);
    opacity: 0;
    transition: opacity 0.3s;
    &:hover {
      opacity: 1;
    }
    .mask,
    .mask-notImg {
      font-size: 16px;
      color: white;
    }
    .mask-notImg {
      opacity: 1;
    }
  }
}
</style>
