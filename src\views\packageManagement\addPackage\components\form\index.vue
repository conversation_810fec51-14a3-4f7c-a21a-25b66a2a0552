<template>
  <div>
    <form-container v-model="visible" :title="`${addPackageProps.opt}加项包`" form-size="95%" @close="onClose">
      <div class="form-container-body">
        <el-form
          ref="addPackageFormRef"
          :rules="rules"
          :disabled="addPackageProps.disabled"
          :model="addPackageProps.record"
          :hide-required-asterisk="addPackageProps.disabled"
          label-width="auto"
          label-suffix=" :"
        >
          <s-form-item label="加项包编码" prop="addPackageCode">
            <s-input v-model="addPackageProps.record.addPackageCode" placeholder="请输入加项包编码" disabled></s-input>
          </s-form-item>
          <s-form-item label="加项包名称" prop="addPackageName">
            <s-input v-model="addPackageProps.record.addPackageName" placeholder="请输入加项包名称"></s-input>
          </s-form-item>
          <s-form-item label="加项包价格" prop="price">
            <el-input-number v-model="addPackageProps.record.price" :precision="2" :min="0" :controls="false" disabled style="width: 260px" />
            <span class="price-unit">元</span>
            <span>(根据选中的体检项目自动计算价格)</span>
          </s-form-item>
          <s-form-item label="适用性别" prop="gender">
            <s-radio-group v-model="addPackageProps.record.gender" :options="genderOptions"></s-radio-group>
          </s-form-item>
          <s-form-item label="启用状态" prop="status">
            <s-radio-group v-model="addPackageProps.record.status" :options="statusOptions"></s-radio-group>
          </s-form-item>
          <s-form-item label="可选项目数量" prop="optionalQuantity">
            <el-input-number v-model="addPackageProps.record.optionalQuantity" :precision="0" :min="0" :controls="false" />
          </s-form-item>
          <s-form-item label="加项包类型" prop="isCompany">
            <s-radio-group
              v-model="addPackageProps.record.isCompany"
              :options="addPackageTypeOptions"
              @change="handleIsCompanyChange"
            ></s-radio-group>
          </s-form-item>
          <s-form-item label="单位名称" prop="companyCode">
            <el-select
              v-model="addPackageProps.record.companyCode"
              filterable
              remote
              reserve-keyword
              clearable
              placeholder="请输入单位名称"
              remote-show-suffix
              :remote-method="remoteMethod"
              :loading="loading"
              :disabled="addPackageProps.record.isCompany === 'N'"
            >
              <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </s-form-item>
          <s-form-item label="加项包简介" prop="introduce">
            <el-input
              v-model="addPackageProps.record.introduce"
              :autosize="{ minRows: 2, maxRows: 4 }"
              type="textarea"
              placeholder="请输入加项包简介"
            />
          </s-form-item>
        </el-form>
        <div class="table-container">
          <el-input v-model="searchCombination" placeholder="请输入组合名称">
            <template #prepend>
              <el-select v-model="searchCombinationOption" placeholder="" style="width: 200px" @change="onSearchCombination">
                <el-option :label="item.label" :value="item.value" v-for="(item, index) in projectClsOptionsMap" :key="index" />
              </el-select>
            </template>
            <template #append>
              <el-button :icon="Search" @click="onSearchCombination" />
            </template>
          </el-input>
          <div class="combination-container">
            <div v-for="(item, index) in filteredCombination" :key="index" class="combination-item">
              <div class="combination-title">{{ item.clsName }}</div>
              <el-checkbox-group v-model="selectedCombination" class="combination-checkbox-group">
                <el-checkbox :label="comb.combCode" border class="combination-checkbox" v-for="(comb, combIndex) in item.combData" :key="combIndex">{{
                  `${comb.combName}　　￥${comb.price}`
                }}</el-checkbox>
              </el-checkbox-group>
            </div>
            <el-empty v-if="filteredCombination.length === 0" description="暂无数据" />
          </div>
        </div>
        <div class="table-container">
          <div class="selected-header">
            <span class="selected-title">已选择组合</span>
            <span class="selected-count">共 {{ selectedCombination.length }} 项</span>
            <span class="selected-price">总价: {{ totalPrice.toFixed(2) }}元</span>
          </div>
          <el-table :data="selectedCombinationData" style="width: 100%" height="100%" border>
            <el-table-column prop="combCode" label="组合编码" width="180" />
            <el-table-column prop="combName" label="组合名称" width="180" />
            <el-table-column prop="price" label="价格" width="180" />
            <el-table-column fixed="right" label="操作" width="135">
              <template #default="scope">
                <el-button link type="danger" @click="onRemoveCombination(scope.row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!addPackageProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { AddPackage, IndividualPackage, addPackageApi, individualPackageApi, companyManagementApi } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";
import { Search } from "@element-plus/icons-vue";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
// 性别选项
const genderOptions = dictStore.getDictList(SysDictEnum.GENDER);
// 项目分类选项
const projectClsOptions = dictStore.getDictList(SysDictEnum.PROJECT_CLS);
const addPackageTypeOptions = [
  { label: "个人", value: "N" },
  { label: "团体", value: "Y" }
]; //类型选项
// 筛选后的组合数据
const filteredCombination = ref<IndividualPackage.CombsByItemCls[]>([]);
// 组合数据
const selectCombination = ref<IndividualPackage.CombsByItemCls[]>([]);
// 已选择组合数据
const selectedCombination = ref<string[]>([]);
// 搜索组合名称
const searchCombination = ref("");
// 搜索项目分类选项
const searchCombinationOption = ref("-1");
const loading = ref(false);
const companyOptions = ref<{ label: string; value: string }[]>([]);
// 表单参数
const addPackageProps = reactive<FormProps.Base<AddPackage.AddPackageInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  addPackageName: [required("请输入加项包名称")],
  gender: [required("请选择适用性别")],
  status: [required("请选择启用状态")],
  optionalQuantity: [required("请输入可选项目数量")],
  isCompany: [required("请选择加项包类型")]
});

// 计算总价
const totalPrice = computed(() => {
  const codes = selectedCombination.value;
  const allCombs = selectCombination.value.flatMap(item => item.combData);
  const total = allCombs.filter(comb => codes.includes(comb.combCode)).reduce((sum, item) => sum + item.price, 0);
  addPackageProps.record.price = Number(total.toFixed(2));
  return Number(total.toFixed(2));
});
/** 计算已选择组合的数据 */
const selectedCombinationData = computed(() => {
  const codes = selectedCombination.value;
  const allCombs = selectCombination.value.flatMap(item => item.combData);
  return allCombs.filter(comb => codes.includes(comb.combCode));
});
/** 计算项目分类选项 */
const projectClsOptionsMap = computed(() => {
  return [{ value: "-1", label: "全部项目分类" }, ...projectClsOptions];
});

/**
 * 打开表单
 * @param props 表单参数
 */
async function onOpen(props: FormProps.Base<AddPackage.AddPackageInfo>) {
  Object.assign(addPackageProps, props); //合并参数
  await getCombsByItemCls();
  if (props.opt == FormOptEnum.ADD) {
    addPackageFormRef.value?.resetFields();
    selectedCombination.value = [];
    //如果是新增,设置默认值
    addPackageProps.record = {
      id: "",
      addPackageCode: "",
      addPackageName: "",
      price: 0,
      gender: genderOptions[0].value,
      status: statusOptions[0].value,
      introduce: "",
      optionalQuantity: 0,
      companyCode: "",
      isCompany: addPackageTypeOptions[0].value
    };
  }
  visible.value = true; //显示表单
  if (props.opt == FormOptEnum.EDIT) {
    addPackageProps.record = JSON.parse(JSON.stringify(props.record));
    addPackageApi.detail({ addPkgCode: props.record.addPackageCode! }).then(res => {
      selectedCombination.value = res.data.map(item => item.combCode);
    });
    companyOptions.value = [{ label: props.record.companyName, value: props.record.companyCode }];
  }
}

// 提交数据（新增/编辑）
const addPackageFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  addPackageFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    const { companyName, ...restRecord } = addPackageProps.record;
    const params = {
      ...restRecord,
      detailCombs: selectedCombinationData.value.map(item => ({
        addPackageCode: addPackageProps.record.addPackageCode,
        combCode: item.combCode
      }))
    };
    //提交表单
    await addPackageApi
      .submitForm(params)
      .then(() => {
        ElMessage.success("保存成功");
        addPackageProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}
// 单位远程搜索
async function remoteMethod(query: string) {
  if (query?.trim()) {
    // 添加可选链操作防止空值
    try {
      const { data } = await companyManagementApi.page({
        pageNum: 1,
        pageSize: 50,
        searchKey: query.trim() // 增加trim处理
      });
      companyOptions.value = data.list.map(item => ({
        label: item.companyName,
        value: item.companyCode || "" // 确保value不为undefined
      }));
    } catch (error) {
      companyOptions.value = [];
      console.error("单位搜索失败:", error);
    }
  } else {
    companyOptions.value = [];
  }
}
function handleIsCompanyChange(value: string) {
  if (value === "N") {
    addPackageProps.record.companyCode = "";
  }
}
/** 搜索组合 */
function onSearchCombination() {
  // 先根据分类过滤基础数据
  const baseData =
    searchCombinationOption.value === "-1"
      ? selectCombination.value
      : selectCombination.value.filter(item => item.clsCode === searchCombinationOption.value);

  // 再根据搜索词过滤组合名称
  filteredCombination.value = baseData
    .map(item => ({
      ...item,
      combData: item.combData.filter(comb => comb.combName.includes(searchCombination.value))
    }))
    .filter(item => item.combData.length > 0);
}
/**
 * 移除组合
 * @param row 组合数据
 */
function onRemoveCombination(row: IndividualPackage.CombinationData) {
  selectedCombination.value = selectedCombination.value.filter(item => item !== row.combCode);
}
/** 获取项目组合 */
async function getCombsByItemCls() {
  const { data } = await individualPackageApi.getCombsByItemCls();
  filteredCombination.value = data;
  selectCombination.value = data;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container-body {
  display: grid;
  grid-template-rows: 70vh;
  grid-template-columns: 400px 1fr 1fr;
  gap: 15px;
  margin-top: 20px;
  .price-unit {
    margin-left: 10px;
  }
  .age-limit {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .table-container {
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
    .selected-header {
      display: grid;
      grid-template-columns: 86px 1fr 1fr;
      align-items: center;
      padding: 6px 8px;
      font-weight: bold;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
    .combination-container {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px;
      overflow: auto;
      border-radius: 4px;
      outline: 1px solid var(--el-border-color);
      .combination-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .combination-title {
        font-weight: bold;
      }
      .combination-checkbox-group {
        margin-left: 20px;
      }
      .combination-checkbox {
        margin-bottom: 20px;
      }
    }
    .selected-count {
      font-weight: normal;
    }
    .selected-price {
      color: var(--el-color-danger);
      text-align: right;
    }
  }
}
:deep(.el-dialog__body) {
  margin-top: 0;
}
:deep(.el-drawer__body) {
  margin-top: -20px;
}
:deep(.el-input-number) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding: 0;
  .el-input__inner {
    font-size: 1.1em;
    text-align: right;
  }
}
</style>
