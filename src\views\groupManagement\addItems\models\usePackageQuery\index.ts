import { companyManagementApi } from "@/api";

interface PackageQuery {
  companyCode: string;
  companyTimes: number | null;
  packageName: string;
}

const defaultPackageQuery: PackageQuery = {
  companyCode: "",
  companyTimes: null,
  packageName: ""
};

export const usePackageQuery = () => {
  let packageQuery = ref<PackageQuery>(deepClone(defaultPackageQuery));

  function reset(value: PackageQuery) {
    packageQuery = deepClone(defaultPackageQuery);
  }

  function setSome<K extends keyof PackageQuery>(key: K, value: PackageQuery[K]) {
    packageQuery.value[key] = value;
  }

  async function searchUnits(currentPage: number, pageSizesItem: number, searchKey: string) {
    const { data } = await companyManagementApi.page({
      pageNum: currentPage,
      pageSize: pageSizesItem,
      searchKey
    });
    const { list, pageNum, pageSize, total } = data;
    return { list, pageNum, pageSize, total };
  }

  return {
    packageQuery,
    setSome,
    reset,
    searchUnits
  };
};

function deepClone(obj: any) {
  return JSON.parse(JSON.stringify(obj));
}
