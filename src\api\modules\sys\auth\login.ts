/**
 * @description 登录模块接口
 */
import { Login } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/auth/");
const request = moduleRequest("/admin/basic/");

const loginApi = {
  /** 用户登录 */
  login(params: Login.LoginForm) {
    return http.post<Login.Login>("login", params, { loading: false });
  },
  /** 获取验证码 */
  picCaptcha() {
    return http.get<Login.ReqValidCode>("getPicCaptcha", {}, { loading: false });
  },
  /** 用户退出登录 */
  logout(params: Login.Logout) {
    return http.post("loginOut", params);
  },
  /** 获取用户信息 */
  getLoginUser() {
    return http.get<Login.LoginUserInfo>("getLoginUser", {}, { loading: false });
  }
};

export { loginApi };
