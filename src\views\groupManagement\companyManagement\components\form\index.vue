<template>
  <div>
    <form-container v-model="visible" :title="`${companyProps.opt}单位`" form-size="800px" @close="onClose">
      <el-form
        class="form"
        ref="companyFormRef"
        :rules="rules"
        :disabled="companyProps.disabled"
        :model="companyProps.record"
        :hide-required-asterisk="companyProps.disabled"
        label-width="auto"
        label-suffix=" :"
      >
        <s-form-item label="单位编码" prop="companyCode">
          <s-input v-model="companyProps.record.companyCode" placeholder=""></s-input>
        </s-form-item>
        <s-form-item label="单位名称" prop="companyName">
          <s-input v-model="companyProps.record.companyName" placeholder=""></s-input>
        </s-form-item>
        <s-form-item label="状态" prop="status">
          <s-radio-group v-model="companyProps.record.status" :options="statusOptions" button />
        </s-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!companyProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { CompanyManagement } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";

const dictStore = useDictStore(); //字典仓库
const visible = ref(false); //是否显示表单
// 通用状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);

// 表单参数
const companyProps = reactive<FormProps.Base<CompanyManagement.Company>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  companyCode: [required("请输入单位编码")],
  companyName: [required("请输入单位名称")],
  status: [required("请选择状态")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<CompanyManagement.Company>) {
  Object.assign(companyProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    companyProps.record.status = statusOptions[0].value;
  }
  visible.value = true; //显示表单
  // if (props.record.id) {
  //   //如果传了id，就去请求api获取record
  //   sysUserApi.detail({ id: props.record.id }).then(res => {
  //     sysUserProps.record = res.data;
  //   });
  // }
}

// 提交数据（新增/编辑）
const companyFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  companyFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    // await sysUserApi
    //   .submitForm(sysUserProps.record, sysUserProps.record.id != undefined)
    //   .then(() => {
    //     sysUserProps.successful!(); //调用父组件的successful方法
    //   })
    //   .finally(() => {
    //     onClose();
    //   });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container {
  .form {
    margin-top: 20px;
  }
  :deep(.el-dialog__body) {
    margin-top: 0;
  }
  :deep(.el-drawer__body) {
    margin-top: -20px;
  }
}
</style>
