<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable
        ref="proTable"
        title="项目分类列表"
        :columns="columns"
        :data="tableData"
        @search="onSearch"
        @reset="onSearch"
        :pagination="false"
        :toolButton="['setting', 'search']"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <s-button suffix="分类" @click="onOpen(FormOptEnum.ADD)" />
          <s-button type="success" icon="Refresh" prefix="同步" suffix="分类" @click="onRefresh" />
          <s-button
            type="danger"
            plain
            suffix="分类"
            :opt="FormOptEnum.DELETE"
            :disabled="!scope.isSelected"
            @click="onDelete(scope.selectedListIds, '删除所选分类')"
          />
        </template>
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
            <s-button link :opt="FormOptEnum.DELETE" @click="onDelete([scope.row.id], `删除【${scope.row.clsName}】`)" />
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script lang="ts" setup name="projectClassification">
import { projectClassificationApi, ProjectClassification } from "@/api";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { FormOptEnum } from "@/enums";
import { useHandleData } from "@/hooks/useHandleData";
import Form from "./components/form/index.vue";

// 表格配置项
const columns: ColumnProps<ProjectClassification.ProjectClassificationInfo>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "searchKey", label: "编码或名称", search: { el: "input" }, isShow: false },
  { prop: "clsCode", label: "分类编码" },
  { prop: "clsName", label: "分类名称" },
  { prop: "shortName", label: "分类简称" },
  { prop: "createDate", label: "创建时间" },
  { prop: "operation", label: "操作", width: 230, fixed: "right" }
];
// 表格数据
const tableData = ref<ProjectClassification.ProjectClassificationInfo[]>([]);
// 过滤后的数据
const filterTable = ref<ProjectClassification.ProjectClassificationInfo[]>([]);

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);
// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

/**
 * 搜索
 */
async function onSearch() {
  const searchKey = proTable.value?.searchParam.searchKey;
  if (searchKey) {
    tableData.value = filterTable.value.filter(item =>
      [item.clsCode, item.clsName].some(field => field.toLowerCase().includes(searchKey.toLowerCase()))
    );
  } else {
    tableData.value = filterTable.value;
  }
}
/**
 * 获取项目分类列表
 */
async function getPage() {
  const { data } = await projectClassificationApi.page();
  tableData.value = data;
  filterTable.value = data;
}
/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | ProjectClassification.ProjectClassificationInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.clearSelection();
  getPage();
}

/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(projectClassificationApi.delete, { ids }, msg);
  RefreshTable();
}
/**
 * 同步项目分类
 */
async function onRefresh() {
  await projectClassificationApi.synchronization().then(() => {
    ElMessage.success("同步成功");
    RefreshTable();
  });
}

onMounted(() => {
  getPage();
});
</script>

<style lang="scss" scoped></style>
