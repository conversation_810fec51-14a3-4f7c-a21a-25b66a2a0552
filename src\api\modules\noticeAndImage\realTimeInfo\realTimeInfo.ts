import { moduleRequest } from "@/api/request";
import { ResPage, sysRealTimeInfo, ReqId, ResultData } from "@/api";
const http = moduleRequest("/admin/NoticeAndImage/");
const http_dev = moduleRequest("/dev/Upload/");

const realTimeInfoApi = {
  /** 分页 */
  page(params: sysRealTimeInfo.Page) {
    return http.post<any[]>("GetTimeInfos", params);
  },
  /** 编辑体检须知:更新内容、新增 */
  EditRealTimeInfo(params: {}, edit: boolean = true) {
    return http.post<Boolean>(edit ? "UpdateRealTimeInfos" : "AddRealTimeInfos", params);
  },
  /** 更新内容 */
  UpdateRealTimeInfoContentById(params: {}, edit: boolean = true) {
    return http.post<Boolean>("UpdateRealTimeInfoContentById", params);
  },
  /**更新资讯状态 */
  UpdateRealTimeInfoStatus(params: sysRealTimeInfo.realTimeInfoStatus) {
    return http.post<Boolean>("UpdateRealTimeInfoStatus", params);
  },
  /** 删除资讯 */
  DeleteRealTimeInfo(params: ReqId[]) {
    return http.post<Boolean>("DeleteRealTimeInfo", params);
  },
  /** 资讯上传图片 */
  UploadRealTimeInfoImg(params: any) {
    // return http_dev.post<sysRealTimeInfo.returnUploadFile>(`upload?type=${encodeURIComponent(type)}`, params);
    return http_dev.post<sysRealTimeInfo.returnUploadFile>("upload?type=REAL_TIME_INFO", params);
  },

  /** 根据id获取资讯内容 */
  GetRealTimeContent(params: {}) {
    return http.post<string>("GetRealTimeContent", params);
  }
};

export { realTimeInfoApi };
