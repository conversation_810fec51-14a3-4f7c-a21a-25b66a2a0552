<!--  用户管理 -->
<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable ref="proTable" title="用户列表" :columns="columns" :request-api="sysUserApi.page" :init-param="initParam">
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <s-button suffix="用户" @click="onOpen(FormOptEnum.ADD)" />
          <s-button
            type="danger"
            plain
            v-auth="userButtonCode.操作用户"
            suffix="用户"
            :opt="FormOptEnum.DELETE"
            :disabled="!scope.isSelected"
            @click="onDelete(scope.selectedListIds, '删除所选用户')"
          />
        </template>
        <!-- 状态 -->
        <template #avatar="scope">
          <el-avatar :src="scope.row.avatar" :size="30" />
        </template>
        <!-- 状态 -->
        <template #status="scope">
          <el-switch
            v-auth="userButtonCode.操作用户"
            :model-value="scope.row.status === CommonStatusEnum.ENABLE"
            :loading="switchLoading"
            @change="editStatus(scope.row)"
          />
        </template>
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
            <s-button
              v-auth="userButtonCode.操作用户"
              link
              :opt="FormOptEnum.DELETE"
              @click="onDelete([scope.row.id], `删除【${scope.row.account}】用户`)"
            />
            <el-dropdown @command="handleCommand">
              <el-link type="primary" :underline="false" :icon="ArrowDown"> 更多 </el-link>
              <template #dropdown>
                <el-dropdown-menu>
                  <div v-auth="userButtonCode.授权角色">
                    <el-dropdown-item key="1" :command="command(scope.row, cmdEnum.GrantRole)">{{ cmdEnum.GrantRole }}</el-dropdown-item>
                  </div>
                  <div v-auth="userButtonCode.重置密码">
                    <el-dropdown-item key="2" :command="command(scope.row, cmdEnum.ResetPassword)">{{ cmdEnum.ResetPassword }}</el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
      <!-- 角色选择器组件 -->
      <role-selector ref="roleSelectorRef" multiple :role-selector-api="sysRoleApi.roleSelector" @successful="handleChooseRole"></role-selector>
    </div>
  </div>
</template>

<script setup lang="ts" name="SysUser">
import { sysUserApi, SysUser, SysRole, sysRoleApi, userButtonCode } from "@/api";
import { ArrowDown } from "@element-plus/icons-vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { SysDictEnum, FormOptEnum, CommonStatusEnum } from "@/enums";
import { useHandleData } from "@/hooks/useHandleData";
import { useDictStore, useUserStore } from "@/stores/modules";
import Form from "./components/form/index.vue";
import { ElMessage } from "element-plus";
import { RoleSelectorInstance } from "@/components/Selectors/RoleSelector/interface";
const dictStore = useDictStore(); //字典仓库
const userStore = useUserStore(); //用户信息

// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
//状态开关loading
const switchLoading = ref(false);

interface InitParam {
  sortField: string;
  orgChoose: string;
}

const uniqueOrgCodes = Array.from(new Set(userStore.orgList.map(item => item.orgCode)));

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<InitParam>({ sortField: "", orgChoose: uniqueOrgCodes.join(",") });

// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

// 表格配置项
const columns: ColumnProps<SysUser.SysUserInfo>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "searchKey", label: "姓名或账号", search: { el: "input" }, isShow: false },
  { prop: "avatar", label: "头像", width: 80 },
  { prop: "account", label: "账号" },
  { prop: "name", label: "姓名" },
  { prop: "phone", label: "手机号" },
  { prop: "email", label: "邮箱" },
  {
    prop: "status",
    label: "状态",
    width: 90,
    enum: statusOptions,
    search: { el: "tree-select" }
  },
  { prop: "createDate", label: "创建时间" },
  { prop: "operation", label: "操作", width: 230, fixed: "right" }
];

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);

/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | SysUser.SysUserInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(sysUserApi.delete, { ids }, msg);
  RefreshTable();
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.refresh(); //刷新表格
}

/** 更多下拉菜单命令枚举 */
enum cmdEnum {
  ResetPassword = "重置密码",
  GrantRole = "授权角色"
}

/** 下拉菜单参数接口 */
interface Command {
  row: SysUser.SysUserInfo;
  command: cmdEnum;
}

/**配置command的参数 */
function command(row: SysUser.SysUserInfo, command: cmdEnum): Command {
  return {
    row: row,
    command: command
  };
}

const roleSelectorRef = ref<RoleSelectorInstance>(); //角色选择器引用
const userId = ref<number | string>(0); //用户id
/**
 * 更多下拉菜单点击事件
 * @param command
 */
function handleCommand(command: Command) {
  switch (command.command) {
    case cmdEnum.ResetPassword:
      useHandleData(sysUserApi.resetPassword, { id: command.row.id }, cmdEnum.ResetPassword);
      break;
    case cmdEnum.GrantRole:
      userId.value = command.row.id; //获取用户id
      sysUserApi.ownRoleRoleSelect({ id: command.row.id }).then(res => {
        roleSelectorRef.value?.showSelector(res.data); //显示用户选择器
      });
      break;
  }
}

/** 选择角色 */
function handleChooseRole(data: SysRole.SysRoleInfo[]) {
  //组装参数
  const grantUser: SysUser.GrantRoleReq = {
    id: userId.value,
    roleIdList: data.map(item => item.id) as number[] | string[]
  };
  sysUserApi.grantRole(grantUser); //授权角色
}

/**
 * 修改状态
 * @param row  当前行数据
 */
function editStatus(row: SysUser.SysUserInfo) {
  switchLoading.value = true;
  const status = row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE; // 状态取反
  sysUserApi.updateStatus({ id: row.id }, status).then(() => {
    switchLoading.value = false;
    ElMessage.success("修改成功");
    row.status = status;
  });
}
</script>

<style lang="scss" scoped></style>
