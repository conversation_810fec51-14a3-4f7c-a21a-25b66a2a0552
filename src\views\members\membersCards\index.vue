<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable ref="proTable" title="列表" :columns="columns" :request-api="membersApi.page" :init-param="initParam">
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)" />
          </el-space>
        </template>
      </ProTable>
      <Form ref="formRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { membersApi, SysMembers } from "@/api";
import { ref, reactive } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { FormOptEnum } from "@/enums";
import Form from "./components/form/index.vue";

interface InitParam {
  sortField: string;
}
// 如果表格需要初始化请求参数
const initParam = reactive<InitParam>({
  sortField: ""
});

// 表格配置项
const columns: ColumnProps<SysMembers.Members>[] = [
  // { prop: "id", label: "ID", width: 100 },
  { type: "selection", fixed: "left", width: 50 },
  { prop: "name", label: "姓名", width: 130, search: { el: "input" }, isShow: true },
  { prop: "tel", label: "电话", width: 120, search: { el: "input" }, isShow: true },
  // { prop: "cardType", label: "证件类型", width: 180 },
  // { prop: "idCard", label: "证件号", width: 180, search: { el: "input" }, isShow: true },
  { prop: "cardNo", label: "证件号", width: 120, search: { el: "input" }, isShow: true },

  { prop: "createDate", label: "创建时间", width: 230 },
  { prop: "operation", label: "操作", fixed: "right" }
];

const proTable = ref<ProTableInstance>();
// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);

function onOpen(opt: FormOptEnum, record: {} | SysMembers.Members = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
  // membersApi.InputMembers
}
/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.refresh(); //刷新表格
}
</script>

<style scoped lang="scss">
.unit-order-container {
  padding: 20px;
  .search-card {
    margin-bottom: 20px;
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 18px;
      font-weight: bold;
    }
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  .detail-title {
    padding-left: 10px;
    margin: 20px 0 10px;
    font-size: 16px;
    font-weight: bold;
    border-left: 4px solid #409eff;
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }
  ::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}
</style>
