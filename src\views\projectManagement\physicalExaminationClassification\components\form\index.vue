<template>
  <div>
    <form-container v-model="visible" :title="`${physicalExaminationClassificationProps.opt}分类`" form-size="500px" @close="onClose">
      <el-form
        class="form"
        ref="physicalExaminationClassificationFormRef"
        :rules="rules"
        :disabled="physicalExaminationClassificationProps.disabled"
        :model="physicalExaminationClassificationProps.record"
        :hide-required-asterisk="physicalExaminationClassificationProps.disabled"
        label-width="auto"
        label-suffix=" :"
      >
        <s-form-item label="分类编码" prop="clsCode">
          <s-input
            v-model="physicalExaminationClassificationProps.record.clsCode"
            placeholder="请输入分类编码"
            :disabled="physicalExaminationClassificationProps.opt === FormOptEnum.EDIT"
          ></s-input>
        </s-form-item>
        <s-form-item label="分类名称" prop="clsName">
          <s-input v-model="physicalExaminationClassificationProps.record.clsName" placeholder="请输入分类名称"></s-input>
        </s-form-item>
        <s-form-item label="状态" prop="isEnabled">
          <s-radio-group v-model="physicalExaminationClassificationProps.record.isEnabled" :options="statusOptions"></s-radio-group>
        </s-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onClose"> 取消 </el-button>
        <el-button v-show="!physicalExaminationClassificationProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </form-container>
  </div>
</template>

<script lang="ts" setup>
import { PhysicalExaminationClassification, physicalExaminationClassificationApi } from "@/api";
import { FormOptEnum, SysDictEnum } from "@/enums";
import { required } from "@/utils/formRules";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";

const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库
// 状态选项
const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);

// 表单参数
const physicalExaminationClassificationProps = reactive<FormProps.Base<PhysicalExaminationClassification.PhysicalExaminationClassificationInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  clsCode: [required("请输入分类编码")],
  clsName: [required("请输入分类名称")],
  isEnabled: [required("请选择状态")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<PhysicalExaminationClassification.PhysicalExaminationClassificationInfo>) {
  Object.assign(physicalExaminationClassificationProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    physicalExaminationClassificationProps.record = {
      clsCode: "",
      clsName: "",
      isEnabled: statusOptions[0].value
    };
  }
  visible.value = true; //显示表单
  if (props.opt == FormOptEnum.EDIT) {
    physicalExaminationClassificationProps.record = JSON.parse(JSON.stringify(props.record));
  }
}

// 提交数据（新增/编辑）
const physicalExaminationClassificationFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  physicalExaminationClassificationFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    await physicalExaminationClassificationApi
      .submitForm(physicalExaminationClassificationProps.record, physicalExaminationClassificationProps.opt == FormOptEnum.EDIT)
      .then(() => {
        ElMessage.success("保存成功");
        physicalExaminationClassificationProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>

<style lang="scss" scoped>
.form-container {
  .form {
    margin-top: 20px;
  }
  :deep(.el-dialog__body) {
    margin-top: 0;
  }
  :deep(.el-drawer__body) {
    margin-top: -20px;
  }
}
</style>
